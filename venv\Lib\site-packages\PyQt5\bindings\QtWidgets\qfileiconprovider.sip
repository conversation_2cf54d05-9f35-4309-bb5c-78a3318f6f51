// qfileiconprovider.sip generated by MetaSIP
//
// This file is part of the QtWidgets Python extension module.
//
// Copyright (c) 2024 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt5.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


class QFileIconProvider
{
%TypeHeaderCode
#include <qfileiconprovider.h>
%End

public:
    QFileIconProvider();
    virtual ~QFileIconProvider();

    enum IconType
    {
        Computer,
        Desktop,
        Trashcan,
        Network,
        Drive,
        Folder,
        File,
    };

    virtual QIcon icon(QFileIconProvider::IconType type) const;
    virtual QIcon icon(const QFileInfo &info) const;
    virtual QString type(const QFileInfo &info) const;
%If (Qt_5_2_0 -)

    enum Option
    {
        DontUseCustomDirectoryIcons,
    };

%End
%If (Qt_5_2_0 -)
    typedef QFlags<QFileIconProvider::Option> Options;
%End
%If (Qt_5_2_0 -)
    void setOptions(QFileIconProvider::Options options);
%End
%If (Qt_5_2_0 -)
    QFileIconProvider::Options options() const;
%End

private:
    QFileIconProvider(const QFileIconProvider &);
};

%If (Qt_5_2_0 -)
QFlags<QFileIconProvider::Option> operator|(QFileIconProvider::Option f1, QFlags<QFileIconProvider::Option> f2);
%End
