#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Module de gestion des stocks
Interface complète pour la gestion des mouvements de stock
"""

import logging
from datetime import datetime
from PyQt5.QtWidgets import *
from PyQt5.QtCore import *
from PyQt5.QtGui import *

from src.ui.components.base_widget import ModuleWidget, FormDialog
from src.dal.models.stock import StockMovement, MovementType, MovementReason
from src.dal.models.product import Product
from src.dal.database import db_manager

class StockWidget(ModuleWidget):
    """Widget principal pour la gestion des stocks"""

    def __init__(self, parent=None):
        super().__init__("Gestion des Stocks", parent)
        self.movements = []
        self.products = []
        self.load_data()

        # Ajouter des boutons spécifiques au stock
        self.add_stock_buttons()

    def add_stock_buttons(self):
        """Ajoute des boutons spécifiques à la gestion des stocks"""
        # Trouver la toolbar dans l'interface
        toolbar = None
        for child in self.findChildren(QFrame):
            if child.objectName() == "moduleToolbar":
                toolbar = child
                break

        if not toolbar:
            return

        # Ajouter un séparateur
        separator = QFrame()
        separator.setFrameShape(QFrame.VLine)
        separator.setFrameShadow(QFrame.Sunken)
        toolbar.layout().insertWidget(3, separator)

        # Bouton d'entrée de stock
        self.entry_button = QPushButton("📥 Entrée Stock")
        self.entry_button.setObjectName("actionButton")
        self.entry_button.clicked.connect(lambda: self.stock_movement("entry"))
        toolbar.layout().insertWidget(4, self.entry_button)

        # Bouton de sortie de stock
        self.exit_button = QPushButton("📤 Sortie Stock")
        self.exit_button.setObjectName("actionButton")
        self.exit_button.clicked.connect(lambda: self.stock_movement("exit"))
        toolbar.layout().insertWidget(5, self.exit_button)

        # Bouton d'ajustement
        self.adjust_button = QPushButton("⚖️ Ajustement")
        self.adjust_button.setObjectName("actionButton")
        self.adjust_button.clicked.connect(lambda: self.stock_movement("adjustment"))
        toolbar.layout().insertWidget(6, self.adjust_button)

    def create_data_table(self):
        """Crée le tableau des mouvements de stock"""
        super().create_data_table()

        # Configuration des colonnes
        headers = ["Date", "Type", "Produit", "Quantité", "Motif", "Référence", "Utilisateur"]
        self.data_table.setColumnCount(len(headers))
        self.data_table.setHorizontalHeaderLabels(headers)

        # Ajuster la largeur des colonnes
        header = self.data_table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.ResizeToContents)  # Date
        header.setSectionResizeMode(1, QHeaderView.ResizeToContents)  # Type
        header.setSectionResizeMode(2, QHeaderView.Stretch)           # Produit
        header.setSectionResizeMode(3, QHeaderView.ResizeToContents)  # Quantité
        header.setSectionResizeMode(4, QHeaderView.ResizeToContents)  # Motif
        header.setSectionResizeMode(5, QHeaderView.ResizeToContents)  # Référence
        header.setSectionResizeMode(6, QHeaderView.ResizeToContents)  # Utilisateur

        # Menu contextuel
        self.data_table.setContextMenuPolicy(Qt.CustomContextMenu)
        self.data_table.customContextMenuRequested.connect(self.show_context_menu)

    def load_data(self):
        """Charge les données des mouvements de stock"""
        try:
            with db_manager.get_session() as session:
                # Charger les mouvements avec leurs relations
                movements = session.query(StockMovement).order_by(StockMovement.date.desc()).all()
                products = session.query(Product).filter(Product.track_stock == True).all()

                # Créer des copies détachées
                self.movements = []
                for movement in movements:
                    # Forcer le chargement des relations
                    _ = movement.product
                    _ = movement.user
                    self.movements.append(movement)

                self.products = []
                for product in products:
                    self.products.append(product)

                # Détacher les objets de la session
                session.expunge_all()

                self.populate_table()
                self.update_count(len(self.movements))
                self.show_message(f"{len(self.movements)} mouvements chargés", "success")
        except Exception as e:
            self.logger.error(f"Erreur lors du chargement des mouvements: {e}")
            self.show_message("Erreur lors du chargement", "error")

    def populate_table(self):
        """Remplit le tableau avec les données"""
        self.data_table.setRowCount(len(self.movements))

        for row, movement in enumerate(self.movements):
            # Date
            date_str = movement.date.strftime("%d/%m/%Y %H:%M") if movement.date else ""
            self.data_table.setItem(row, 0, QTableWidgetItem(date_str))

            # Type
            type_text = {
                MovementType.IN: "Entrée",
                MovementType.OUT: "Sortie",
                MovementType.ADJUSTMENT: "Ajustement"
            }.get(movement.movement_type, "Inconnu")

            type_item = QTableWidgetItem(type_text)
            if movement.movement_type == MovementType.IN:
                type_item.setForeground(QColor("#00ff88"))
            elif movement.movement_type == MovementType.OUT:
                type_item.setForeground(QColor("#ff6b6b"))
            else:
                type_item.setForeground(QColor("#ffaa00"))
            self.data_table.setItem(row, 1, type_item)

            # Produit
            product_name = movement.product.name if movement.product else "Produit supprimé"
            self.data_table.setItem(row, 2, QTableWidgetItem(product_name))

            # Quantité
            quantity_text = f"{movement.quantity:+.2f}"
            quantity_item = QTableWidgetItem(quantity_text)
            if movement.quantity > 0:
                quantity_item.setForeground(QColor("#00ff88"))
            else:
                quantity_item.setForeground(QColor("#ff6b6b"))
            self.data_table.setItem(row, 3, quantity_item)

            # Motif
            reason_text = {
                MovementReason.PURCHASE: "Achat",
                MovementReason.SALE: "Vente",
                MovementReason.RETURN_CUSTOMER: "Retour client",
                MovementReason.RETURN_SUPPLIER: "Retour fournisseur",
                MovementReason.INVENTORY: "Inventaire",
                MovementReason.LOSS: "Perte",
                MovementReason.DAMAGE: "Dommage",
                MovementReason.TRANSFER: "Transfert",
                MovementReason.PRODUCTION: "Production",
                MovementReason.OTHER: "Autre"
            }.get(movement.reason, "Non spécifié")
            self.data_table.setItem(row, 4, QTableWidgetItem(reason_text))

            # Référence
            self.data_table.setItem(row, 5, QTableWidgetItem(movement.reference or ""))

            # Utilisateur
            user_name = movement.user.username if movement.user else "Système"
            self.data_table.setItem(row, 6, QTableWidgetItem(user_name))

    def get_item_from_row(self, row):
        """Récupère le mouvement depuis la ligne sélectionnée"""
        if 0 <= row < len(self.movements):
            return self.movements[row]
        return None

    def handle_action(self, action):
        """Gère les actions du module"""
        if action == "new":
            self.new_movement()
        elif action == "edit" and self.current_item:
            self.edit_movement(self.current_item)
        elif action == "delete" and self.current_item:
            self.delete_movement(self.current_item)

        super().handle_action(action)

    def stock_movement(self, movement_type):
        """Crée un mouvement de stock spécifique"""
        dialog = StockMovementDialog(movement_type, self.products, self)
        if dialog.exec_() == QDialog.Accepted:
            movement_data = dialog.get_data()
            if self.save_movement(movement_data):
                self.refresh_data()

    def new_movement(self):
        """Crée un nouveau mouvement de stock"""
        self.stock_movement("manual")

    def edit_movement(self, movement):
        """Modifie un mouvement existant (limité)"""
        QMessageBox.information(
            self,
            "Modification limitée",
            "Les mouvements de stock ne peuvent être modifiés que dans certains cas.\n"
            "Contactez votre administrateur pour plus d'informations."
        )

    def delete_movement(self, movement):
        """Supprime un mouvement de stock"""
        reply = QMessageBox.question(
            self,
            "Confirmer la suppression",
            f"Êtes-vous sûr de vouloir supprimer ce mouvement de stock ?\n\n"
            f"Produit: {movement.product.name if movement.product else 'N/A'}\n"
            f"Quantité: {movement.quantity:+.2f}\n"
            f"Date: {movement.date.strftime('%d/%m/%Y %H:%M') if movement.date else 'N/A'}\n\n"
            "⚠️ Cette action affectera le stock du produit !",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )

        if reply == QMessageBox.Yes:
            try:
                with db_manager.get_session() as session:
                    movement_to_delete = session.query(StockMovement).filter(
                        StockMovement.id == movement.id
                    ).first()

                    if movement_to_delete:
                        # Annuler l'effet sur le stock
                        product = session.query(Product).filter(
                            Product.id == movement_to_delete.product_id
                        ).first()

                        if product:
                            # Inverser le mouvement
                            product.current_stock -= movement_to_delete.quantity
                            if product.current_stock < 0:
                                product.current_stock = 0

                        session.delete(movement_to_delete)
                        session.commit()

                        self.show_message("Mouvement supprimé et stock ajusté", "success")
                        self.refresh_data()
            except Exception as e:
                self.logger.error(f"Erreur lors de la suppression: {e}")
                self.show_message("Erreur lors de la suppression", "error")

    def save_movement(self, data):
        """Sauvegarde un mouvement de stock"""
        try:
            with db_manager.get_session() as session:
                # Créer le mouvement
                movement = StockMovement(**data)
                session.add(movement)

                # Mettre à jour le stock du produit
                product = session.query(Product).filter(Product.id == data['product_id']).first()
                if product:
                    product.current_stock += data['quantity']
                    if product.current_stock < 0:
                        product.current_stock = 0

                session.commit()
                self.show_message("Mouvement de stock enregistré", "success")
                return True

        except Exception as e:
            self.logger.error(f"Erreur lors de la sauvegarde: {e}")
            self.show_message("Erreur lors de la sauvegarde", "error")
            return False

    def show_context_menu(self, position):
        """Affiche le menu contextuel"""
        if self.data_table.itemAt(position):
            menu = QMenu(self)

            view_action = menu.addAction("👁️ Voir détails")
            view_action.triggered.connect(self.view_movement_details)

            menu.addSeparator()

            delete_action = menu.addAction("🗑️ Supprimer")
            delete_action.triggered.connect(lambda: self.handle_action("delete"))

            menu.exec_(self.data_table.mapToGlobal(position))

    def view_movement_details(self):
        """Affiche les détails du mouvement"""
        if self.current_item:
            dialog = MovementDetailsDialog(self.current_item, self)
            dialog.exec_()

class StockMovementDialog(FormDialog):
    """Formulaire de saisie de mouvement de stock"""

    def __init__(self, movement_type, products, parent=None):
        self.movement_type = movement_type
        self.products = products

        titles = {
            "entry": "Entrée de Stock",
            "exit": "Sortie de Stock",
            "adjustment": "Ajustement de Stock",
            "manual": "Mouvement Manuel"
        }

        super().__init__(titles.get(movement_type, "Mouvement de Stock"), parent)
        self.setFixedSize(500, 600)
        self.setup_form()

    def setup_form(self):
        """Configure le formulaire"""
        # Produit
        self.product_combo = QComboBox()
        self.product_combo.addItem("Sélectionner un produit", None)
        for product in self.products:
            stock_info = f" (Stock: {product.current_stock:.0f})" if product.track_stock else ""
            self.product_combo.addItem(f"{product.name}{stock_info}", product.id)
        self.add_field("Produit *:", self.product_combo)

        # Quantité
        self.quantity_input = QDoubleSpinBox()
        self.quantity_input.setDecimals(3)
        self.quantity_input.setMinimum(-999999.999)
        self.quantity_input.setMaximum(999999.999)

        if self.movement_type == "entry":
            self.quantity_input.setMinimum(0.001)
        elif self.movement_type == "exit":
            self.quantity_input.setMaximum(-0.001)

        self.add_field("Quantité *:", self.quantity_input)

        # Type de mouvement (automatique selon le type)
        movement_types = {
            "entry": MovementType.IN,
            "exit": MovementType.OUT,
            "adjustment": MovementType.ADJUSTMENT,
            "manual": MovementType.ADJUSTMENT
        }
        self.movement_type_value = movement_types.get(self.movement_type, MovementType.ADJUSTMENT)

        # Motif
        self.reason_combo = QComboBox()
        reasons = [
            ("Achat", MovementReason.PURCHASE),
            ("Vente", MovementReason.SALE),
            ("Retour client", MovementReason.RETURN_CUSTOMER),
            ("Retour fournisseur", MovementReason.RETURN_SUPPLIER),
            ("Inventaire", MovementReason.INVENTORY),
            ("Perte", MovementReason.LOSS),
            ("Dommage", MovementReason.DAMAGE),
            ("Transfert", MovementReason.TRANSFER),
            ("Production", MovementReason.PRODUCTION),
            ("Autre", MovementReason.OTHER)
        ]

        for reason_text, reason_value in reasons:
            self.reason_combo.addItem(reason_text, reason_value)

        # Présélectionner selon le type
        if self.movement_type == "entry":
            self.reason_combo.setCurrentText("Achat")
        elif self.movement_type == "exit":
            self.reason_combo.setCurrentText("Vente")

        self.add_field("Motif:", self.reason_combo)

        # Référence
        self.reference_input = QLineEdit()
        self.reference_input.setPlaceholderText("Numéro de facture, bon, etc.")
        self.add_field("Référence:", self.reference_input)

        # Date
        self.date_input = QDateTimeEdit()
        self.date_input.setDateTime(QDateTime.currentDateTime())
        self.date_input.setDisplayFormat("dd/MM/yyyy hh:mm")
        self.add_field("Date:", self.date_input)

        # Notes
        self.notes_input = QTextEdit()
        self.notes_input.setMaximumHeight(80)
        self.notes_input.setPlaceholderText("Notes et commentaires...")
        self.add_field("Notes:", self.notes_input)

        # Connecter les signaux
        self.product_combo.currentTextChanged.connect(self.update_stock_info)

    def update_stock_info(self):
        """Met à jour les informations de stock"""
        product_id = self.product_combo.currentData()
        if product_id:
            for product in self.products:
                if product.id == product_id:
                    if product.track_stock:
                        stock_text = f"Stock actuel: {product.current_stock:.2f}"
                        if product.is_low_stock:
                            stock_text += " ⚠️ Stock bas"
                        elif product.is_out_of_stock:
                            stock_text += " ❌ Rupture"

                        # Afficher dans la barre de statut du parent
                        if hasattr(self.parent(), 'show_message'):
                            self.parent().show_message(stock_text, "info")
                    break

    def get_data(self):
        """Récupère les données du formulaire"""
        from src.ui.main_window import MainWindow

        # Récupérer l'utilisateur actuel depuis la fenêtre principale
        main_window = None
        widget = self.parent()
        while widget and not isinstance(widget, MainWindow):
            widget = widget.parent()

        if widget and hasattr(widget, 'current_user'):
            user_id = widget.current_user.id
        else:
            user_id = 1  # Fallback

        return {
            'product_id': self.product_combo.currentData(),
            'movement_type': self.movement_type_value,
            'quantity': self.quantity_input.value(),
            'reason': self.reason_combo.currentData(),
            'reference': self.reference_input.text().strip() or None,
            'date': self.date_input.dateTime().toPyDateTime(),
            'notes': self.notes_input.toPlainText().strip() or None,
            'user_id': user_id
        }

class MovementDetailsDialog(QDialog):
    """Dialogue d'affichage des détails d'un mouvement"""

    def __init__(self, movement, parent=None):
        super().__init__(parent)
        self.movement = movement
        self.setWindowTitle("Détails du Mouvement")
        self.setFixedSize(500, 400)
        self.setup_ui()
        self.apply_styles()

    def setup_ui(self):
        """Configure l'interface"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(20, 20, 20, 20)

        # En-tête
        header_layout = QHBoxLayout()

        icon_label = QLabel("📦")
        icon_label.setStyleSheet("font-size: 32px;")
        header_layout.addWidget(icon_label)

        title_layout = QVBoxLayout()
        title_label = QLabel("Mouvement de Stock")
        title_label.setStyleSheet("font-size: 18px; font-weight: bold; color: #00d4ff;")
        title_layout.addWidget(title_label)

        date_str = self.movement.date.strftime("%d/%m/%Y à %H:%M") if self.movement.date else "Date inconnue"
        subtitle_label = QLabel(date_str)
        subtitle_label.setStyleSheet("color: rgba(255, 255, 255, 0.7);")
        title_layout.addWidget(subtitle_label)

        header_layout.addLayout(title_layout)
        header_layout.addStretch()

        layout.addLayout(header_layout)

        # Contenu
        content_widget = QWidget()
        content_layout = QVBoxLayout(content_widget)

        # Informations du mouvement
        movement_info = [
            ("Type", {
                MovementType.IN: "Entrée",
                MovementType.OUT: "Sortie",
                MovementType.ADJUSTMENT: "Ajustement"
            }.get(self.movement.movement_type, "Inconnu")),
            ("Produit", self.movement.product.name if self.movement.product else "Produit supprimé"),
            ("Quantité", f"{self.movement.quantity:+.3f}"),
            ("Motif", {
                MovementReason.PURCHASE: "Achat",
                MovementReason.SALE: "Vente",
                MovementReason.RETURN_CUSTOMER: "Retour client",
                MovementReason.RETURN_SUPPLIER: "Retour fournisseur",
                MovementReason.INVENTORY: "Inventaire",
                MovementReason.LOSS: "Perte",
                MovementReason.DAMAGE: "Dommage",
                MovementReason.TRANSFER: "Transfert",
                MovementReason.PRODUCTION: "Production",
                MovementReason.OTHER: "Autre"
            }.get(self.movement.reason, "Non spécifié"))
        ]

        if self.movement.reference:
            movement_info.append(("Référence", self.movement.reference))

        if self.movement.user:
            movement_info.append(("Utilisateur", self.movement.user.username))

        self.add_section(content_layout, "Informations", movement_info)

        # Notes
        if self.movement.notes:
            self.add_section(content_layout, "Notes", [
                ("", self.movement.notes)
            ])

        layout.addWidget(content_widget)

        # Bouton fermer
        close_button = QPushButton("Fermer")
        close_button.clicked.connect(self.accept)
        layout.addWidget(close_button)

    def add_section(self, layout, title, items):
        """Ajoute une section d'informations"""
        # Titre de section
        title_label = QLabel(title)
        title_label.setStyleSheet("""
            font-size: 14px;
            font-weight: bold;
            color: #00d4ff;
            margin-top: 15px;
            margin-bottom: 5px;
        """)
        layout.addWidget(title_label)

        # Contenu de la section
        for label, value in items:
            item_layout = QHBoxLayout()

            if label:
                label_widget = QLabel(f"{label}:")
                label_widget.setStyleSheet("color: rgba(255, 255, 255, 0.8); font-weight: 500;")
                label_widget.setFixedWidth(100)
                item_layout.addWidget(label_widget)

            value_widget = QLabel(str(value))
            value_widget.setStyleSheet("color: white;")
            value_widget.setWordWrap(True)
            item_layout.addWidget(value_widget)

            layout.addLayout(item_layout)

    def apply_styles(self):
        """Applique les styles"""
        self.setStyleSheet("""
            QDialog {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(10, 10, 30, 0.95),
                    stop:1 rgba(20, 20, 50, 0.95));
                border: 1px solid rgba(255, 255, 255, 0.2);
                border-radius: 12px;
            }

            QPushButton {
                background: rgba(255, 255, 255, 0.1);
                border: 1px solid rgba(255, 255, 255, 0.2);
                border-radius: 6px;
                padding: 10px 20px;
                color: white;
                font-weight: 500;
            }

            QPushButton:hover {
                background: rgba(0, 212, 255, 0.2);
                border-color: #00d4ff;
            }
        """)
