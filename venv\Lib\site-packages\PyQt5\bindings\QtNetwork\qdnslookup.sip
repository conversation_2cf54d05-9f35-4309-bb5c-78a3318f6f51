// qdnslookup.sip generated by MetaSIP
//
// This file is part of the QtNetwork Python extension module.
//
// Copyright (c) 2024 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt5.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


class QDnsDomainNameRecord
{
%TypeHeaderCode
#include <qdnslookup.h>
%End

public:
    QDnsDomainNameRecord();
    QDnsDomainNameRecord(const QDnsDomainNameRecord &other);
    ~QDnsDomainNameRecord();
    void swap(QDnsDomainNameRecord &other /Constrained/);
    QString name() const;
    quint32 timeToLive() const;
    QString value() const;
};

class QDnsHostAddressRecord
{
%TypeHeaderCode
#include <qdnslookup.h>
%End

public:
    QDnsHostAddressRecord();
    QDnsHostAddressRecord(const QDnsHostAddressRecord &other);
    ~QDnsHostAddressRecord();
    void swap(QDnsHostAddressRecord &other /Constrained/);
    QString name() const;
    quint32 timeToLive() const;
    QHostAddress value() const;
};

class QDnsMailExchangeRecord
{
%TypeHeaderCode
#include <qdnslookup.h>
%End

public:
    QDnsMailExchangeRecord();
    QDnsMailExchangeRecord(const QDnsMailExchangeRecord &other);
    ~QDnsMailExchangeRecord();
    void swap(QDnsMailExchangeRecord &other /Constrained/);
    QString exchange() const;
    QString name() const;
    quint16 preference() const;
    quint32 timeToLive() const;
};

class QDnsServiceRecord
{
%TypeHeaderCode
#include <qdnslookup.h>
%End

public:
    QDnsServiceRecord();
    QDnsServiceRecord(const QDnsServiceRecord &other);
    ~QDnsServiceRecord();
    void swap(QDnsServiceRecord &other /Constrained/);
    QString name() const;
    quint16 port() const;
    quint16 priority() const;
    QString target() const;
    quint32 timeToLive() const;
    quint16 weight() const;
};

class QDnsTextRecord
{
%TypeHeaderCode
#include <qdnslookup.h>
%End

public:
    QDnsTextRecord();
    QDnsTextRecord(const QDnsTextRecord &other);
    ~QDnsTextRecord();
    void swap(QDnsTextRecord &other /Constrained/);
    QString name() const;
    quint32 timeToLive() const;
    QList<QByteArray> values() const;
};

class QDnsLookup : public QObject
{
%TypeHeaderCode
#include <qdnslookup.h>
%End

public:
    enum Error
    {
        NoError,
        ResolverError,
        OperationCancelledError,
        InvalidRequestError,
        InvalidReplyError,
        ServerFailureError,
        ServerRefusedError,
        NotFoundError,
    };

    enum Type
    {
        A,
        AAAA,
        ANY,
        CNAME,
        MX,
        NS,
        PTR,
        SRV,
        TXT,
    };

    explicit QDnsLookup(QObject *parent /TransferThis/ = 0);
    QDnsLookup(QDnsLookup::Type type, const QString &name, QObject *parent /TransferThis/ = 0);
%If (Qt_5_5_0 -)
    QDnsLookup(QDnsLookup::Type type, const QString &name, const QHostAddress &nameserver, QObject *parent /TransferThis/ = 0);
%End
    virtual ~QDnsLookup();
    QDnsLookup::Error error() const;
    QString errorString() const;
    bool isFinished() const;
    QString name() const;
    void setName(const QString &name);
    QDnsLookup::Type type() const;
    void setType(QDnsLookup::Type);
    QList<QDnsDomainNameRecord> canonicalNameRecords() const;
    QList<QDnsHostAddressRecord> hostAddressRecords() const;
    QList<QDnsMailExchangeRecord> mailExchangeRecords() const;
    QList<QDnsDomainNameRecord> nameServerRecords() const;
    QList<QDnsDomainNameRecord> pointerRecords() const;
    QList<QDnsServiceRecord> serviceRecords() const;
    QList<QDnsTextRecord> textRecords() const;

public slots:
    void abort() /ReleaseGIL/;
    void lookup() /ReleaseGIL/;

signals:
    void finished();
    void nameChanged(const QString &name);
    void typeChanged(QDnsLookup::Type type /ScopesStripped=1/);

public:
%If (Qt_5_3_0 -)
    QHostAddress nameserver() const;
%End
%If (Qt_5_3_0 -)
    void setNameserver(const QHostAddress &nameserver);
%End

signals:
%If (Qt_5_3_0 -)
    void nameserverChanged(const QHostAddress &nameserver);
%End
};
