# GSCOM - Application de Gestion Commerciale, Stock, Finance, Comptabilité et Inventaire

## 🚀 Description

GSCOM est une application complète de gestion d'entreprise développée en Python avec PyQt5. Elle offre une solution intégrée pour la gestion commerciale, des stocks, de la comptabilité, des finances et des inventaires.

## ✨ Fonctionnalités

### 🏢 Modules Principaux

- **📊 Tableau de bord** - Vue d'ensemble avec KPIs et statistiques
- **💼 Gestion Commerciale** - Devis, commandes, livraisons, factures
- **👥 Gestion des Clients** - Base de données clients complète
- **🏭 Gestion des Fournisseurs** - Suivi des fournisseurs et relations
- **📦 Gestion des Produits** - Catalogue produits avec catégories
- **📋 Gestion des Stocks** - Suivi en temps réel des stocks
- **📝 Inventaire** - Outils d'inventaire et réconciliation
- **💰 Comptabilité** - Plan comptable et écritures automatiques
- **📈 Rapports** - Génération de rapports personnalisés
- **⚙️ Paramètres** - Configuration et administration

### 🔐 Sécurité et Utilisateurs

- Système d'authentification sécurisé
- Gestion des rôles et permissions
- Journalisation des actions
- Verrouillage automatique des comptes

### 🎨 Interface Moderne

- Design futuriste avec effets glassmorphism
- Thème sombre par défaut
- Interface responsive et intuitive
- Animations et transitions fluides

## 🏗️ Architecture

L'application suit une architecture en 4 couches :

```
┌─────────────────────────────────────┐
│           UI (Présentation)         │  ← PyQt5, Design moderne
├─────────────────────────────────────┤
│        BLL (Logique Métier)         │  ← Services métier
├─────────────────────────────────────┤
│      DAL (Accès aux Données)        │  ← SQLAlchemy, Modèles
├─────────────────────────────────────┤
│       Services Transverses          │  ← Sécurité, Logs, Config
└─────────────────────────────────────┘
```

### 📁 Structure du Projet

```
GSCOM/
├── src/
│   ├── core/           # Configuration et utilitaires
│   ├── dal/            # Couche d'accès aux données
│   │   └── models/     # Modèles SQLAlchemy
│   ├── bll/            # Couche logique métier
│   └── ui/             # Interface utilisateur
├── resources/          # Ressources (icônes, traductions)
├── main.py            # Point d'entrée
├── init_database.py   # Initialisation DB
└── requirements.txt   # Dépendances
```

## 🛠️ Installation

### Prérequis

- Python 3.8 ou supérieur
- pip (gestionnaire de packages Python)

### Installation des dépendances

```bash
# Cloner le projet
git clone <repository-url>
cd GSCOM

# Créer un environnement virtuel (recommandé)
python -m venv venv

# Activer l'environnement virtuel
# Windows:
venv\Scripts\activate
# Linux/Mac:
source venv/bin/activate

# Installer les dépendances
pip install PyQt5 SQLAlchemy bcrypt
```

### Initialisation de la base de données

```bash
# Initialisation simple (recommandée pour les tests)
python simple_init.py

# Ou initialisation complète avec permissions
python init_database.py
```

## 🚀 Utilisation

### Démarrage de l'application

```bash
python main.py
```

### Connexion par défaut

- **Nom d'utilisateur :** `admin`
- **Mot de passe :** `admin123`

### Première utilisation

1. Lancez l'application avec `python main.py`
2. Connectez-vous avec les identifiants par défaut
3. Explorez les différents modules via la navigation latérale
4. Configurez les paramètres de votre entreprise
5. Commencez à saisir vos données

## 🔧 Configuration

### Base de données

Par défaut, l'application utilise SQLite. La configuration se trouve dans `src/core/config.py`.

Pour utiliser PostgreSQL :

```python
# Dans config.py, modifier la section database
"database": {
    "type": "postgresql",
    "postgresql": {
        "host": "localhost",
        "port": 5432,
        "database": "gscom",
        "username": "votre_utilisateur",
        "password": "votre_mot_de_passe"
    }
}
```

### Thèmes

L'application supporte les thèmes sombre et clair. Configuration dans les paramètres utilisateur.

## 📊 Modèles de Données

### Principaux modèles

- **User** - Utilisateurs et authentification
- **Client/Supplier** - Clients et fournisseurs
- **Product** - Produits avec catégories
- **Invoice** - Factures et lignes de facture
- **StockMovement** - Mouvements de stock
- **Account** - Plan comptable

### Relations

- Un client peut avoir plusieurs factures
- Une facture contient plusieurs lignes
- Chaque ligne référence un produit
- Les mouvements de stock sont liés aux produits

## 🔐 Sécurité

### Authentification

- Mots de passe hashés avec bcrypt
- Verrouillage automatique après échecs
- Sessions avec timeout configurable

### Permissions

- Système de rôles granulaire
- Permissions par module et action
- Journalisation des actions sensibles

## 📈 Fonctionnalités Avancées

### Rapports

- Génération PDF avec ReportLab
- Export Excel avec openpyxl
- Graphiques avec matplotlib

### Codes-barres

- Génération de codes-barres
- Lecture via caméra (OpenCV + pyzbar)
- Intégration avec la gestion des produits

### Sauvegarde

- Sauvegarde automatique de la base
- Export/Import des données
- Points de restauration

## 🌍 Internationalisation

- Support multilingue (français par défaut)
- Devise configurable (DA - Dinar Algérien)
- Formats de date localisés

## 🐛 Dépannage

### Problèmes courants

1. **Erreur de base de données**
   ```bash
   python simple_init.py
   ```

2. **Problème d'affichage**
   - Vérifiez la résolution d'écran
   - Testez avec un autre thème

3. **Erreur de permissions**
   - Vérifiez les droits d'écriture
   - Relancez en tant qu'administrateur

### Logs

Les logs sont stockés dans `~/.gscom/logs/` et contiennent les informations de débogage.

## 🤝 Contribution

### Structure de développement

1. Respecter l'architecture en couches
2. Documenter les nouvelles fonctionnalités
3. Tester les modifications
4. Suivre les conventions de nommage Python

### Ajout de modules

1. Créer le modèle dans `src/dal/models/`
2. Implémenter le service dans `src/bll/`
3. Créer l'interface dans `src/ui/`
4. Mettre à jour la navigation

## 📝 Licence

© 2024 GSCOM Solutions - Tous droits réservés

## 📞 Support

Pour toute question ou support technique, contactez l'équipe de développement.

---

**GSCOM** - *Votre solution complète de gestion d'entreprise* 🚀
