// qgeomaneuver.sip generated by MetaSIP
//
// This file is part of the QtLocation Python extension module.
//
// Copyright (c) 2024 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt5.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


%If (Qt_5_5_0 -)

class QGeoManeuver
{
%TypeHeaderCode
#include <qgeomaneuver.h>
%End

public:
    enum InstructionDirection
    {
        NoDirection,
        DirectionForward,
        DirectionBearRight,
        DirectionLightRight,
        DirectionRight,
        DirectionHardRight,
        DirectionUTurnRight,
        DirectionUTurnLeft,
        DirectionHardLeft,
        DirectionLeft,
        DirectionLightLeft,
        DirectionBearLeft,
    };

    QGeoManeuver();
    QGeoManeuver(const QGeoManeuver &other);
    ~QGeoManeuver();
    bool operator==(const QGeoManeuver &other) const;
    bool operator!=(const QGeoManeuver &other) const;
    bool isValid() const;
    void setPosition(const QGeoCoordinate &position);
    QGeoCoordinate position() const;
    void setInstructionText(const QString &instructionText);
    QString instructionText() const;
    void setDirection(QGeoManeuver::InstructionDirection direction);
    QGeoManeuver::InstructionDirection direction() const;
    void setTimeToNextInstruction(int secs);
    int timeToNextInstruction() const;
    void setDistanceToNextInstruction(qreal distance);
    qreal distanceToNextInstruction() const;
    void setWaypoint(const QGeoCoordinate &coordinate);
    QGeoCoordinate waypoint() const;
%If (Qt_5_11_0 -)
    void setExtendedAttributes(const QVariantMap &extendedAttributes);
%End
%If (Qt_5_11_0 -)
    QVariantMap extendedAttributes() const;
%End
};

%End
