#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Module de rapports et analyses
Interface pour la génération de rapports
"""

import logging
from datetime import datetime, timedelta
from PyQt5.QtWidgets import *
from PyQt5.QtCore import *
from PyQt5.QtGui import *

from src.ui.components.base_widget import ModuleWidget
from src.dal.database import db_manager
from src.dal.models.client import Client
from src.dal.models.product import Product
from src.dal.models.stock import StockMovement, MovementType
from src.dal.models.user import User

class ReportsWidget(ModuleWidget):
    """Widget principal pour les rapports"""
    
    def __init__(self, parent=None):
        super().__init__("Rapports et Analyses", parent)
        self.setup_reports_ui()
        self.load_report_data()
    
    def setup_reports_ui(self):
        """Configure l'interface spécifique aux rapports"""
        # Remplacer le tableau par une interface de rapports
        self.content_layout.removeWidget(self.data_table)
        self.data_table.hide()
        
        # Créer l'interface de rapports
        self.create_reports_interface()
    
    def create_reports_interface(self):
        """Crée l'interface de rapports"""
        # Layout principal avec onglets
        self.tabs = QTabWidget()
        self.tabs.setObjectName("reportTabs")
        
        # Onglet Vue d'ensemble
        self.create_overview_tab()
        
        # Onglet Clients
        self.create_clients_tab()
        
        # Onglet Produits
        self.create_products_tab()
        
        # Onglet Stock
        self.create_stock_tab()
        
        self.content_layout.addWidget(self.tabs)
    
    def create_overview_tab(self):
        """Crée l'onglet vue d'ensemble"""
        overview_widget = QWidget()
        layout = QVBoxLayout(overview_widget)
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(20)
        
        # Titre
        title = QLabel("📊 Vue d'Ensemble")
        title.setObjectName("reportTitle")
        layout.addWidget(title)
        
        # Cartes de résumé
        cards_layout = QHBoxLayout()
        
        # Carte Clients
        self.clients_summary_card = self.create_summary_card(
            "👥", "Clients", "0", "0 actifs"
        )
        cards_layout.addWidget(self.clients_summary_card)
        
        # Carte Produits
        self.products_summary_card = self.create_summary_card(
            "📦", "Produits", "0", "0 en stock"
        )
        cards_layout.addWidget(self.products_summary_card)
        
        # Carte Mouvements
        self.movements_summary_card = self.create_summary_card(
            "🔄", "Mouvements", "0", "7 derniers jours"
        )
        cards_layout.addWidget(self.movements_summary_card)
        
        # Carte Valeur Stock
        self.value_summary_card = self.create_summary_card(
            "💰", "Valeur Stock", "0 DA", "Prix d'achat"
        )
        cards_layout.addWidget(self.value_summary_card)
        
        layout.addLayout(cards_layout)
        
        # Graphiques et analyses
        charts_layout = QHBoxLayout()
        
        # Zone de graphique (placeholder)
        chart_frame = QFrame()
        chart_frame.setObjectName("chartFrame")
        chart_frame.setFixedHeight(300)
        chart_layout = QVBoxLayout(chart_frame)
        
        chart_title = QLabel("📈 Évolution des Stocks (7 derniers jours)")
        chart_title.setObjectName("chartTitle")
        chart_layout.addWidget(chart_title)
        
        chart_placeholder = QLabel("Graphique en cours de développement\n\n"
                                 "Fonctionnalités prévues :\n"
                                 "• Évolution des stocks\n"
                                 "• Mouvements par période\n"
                                 "• Analyse des ventes\n"
                                 "• Tendances clients")
        chart_placeholder.setObjectName("chartPlaceholder")
        chart_placeholder.setAlignment(Qt.AlignCenter)
        chart_layout.addWidget(chart_placeholder)
        
        charts_layout.addWidget(chart_frame)
        
        # Zone d'alertes
        alerts_frame = QFrame()
        alerts_frame.setObjectName("alertsFrame")
        alerts_frame.setFixedHeight(300)
        alerts_layout = QVBoxLayout(alerts_frame)
        
        alerts_title = QLabel("⚠️ Alertes et Notifications")
        alerts_title.setObjectName("alertsTitle")
        alerts_layout.addWidget(alerts_title)
        
        self.alerts_list = QListWidget()
        self.alerts_list.setObjectName("alertsList")
        alerts_layout.addWidget(self.alerts_list)
        
        charts_layout.addWidget(alerts_frame)
        
        layout.addLayout(charts_layout)
        
        self.tabs.addTab(overview_widget, "Vue d'Ensemble")
    
    def create_clients_tab(self):
        """Crée l'onglet rapports clients"""
        clients_widget = QWidget()
        layout = QVBoxLayout(clients_widget)
        layout.setContentsMargins(20, 20, 20, 20)
        
        # Titre
        title = QLabel("👥 Rapport Clients")
        title.setObjectName("reportTitle")
        layout.addWidget(title)
        
        # Statistiques clients
        self.clients_stats_table = QTableWidget()
        self.clients_stats_table.setObjectName("statsTable")
        layout.addWidget(self.clients_stats_table)
        
        self.tabs.addTab(clients_widget, "Clients")
    
    def create_products_tab(self):
        """Crée l'onglet rapports produits"""
        products_widget = QWidget()
        layout = QVBoxLayout(products_widget)
        layout.setContentsMargins(20, 20, 20, 20)
        
        # Titre
        title = QLabel("📦 Rapport Produits")
        title.setObjectName("reportTitle")
        layout.addWidget(title)
        
        # Statistiques produits
        self.products_stats_table = QTableWidget()
        self.products_stats_table.setObjectName("statsTable")
        layout.addWidget(self.products_stats_table)
        
        self.tabs.addTab(products_widget, "Produits")
    
    def create_stock_tab(self):
        """Crée l'onglet rapports stock"""
        stock_widget = QWidget()
        layout = QVBoxLayout(stock_widget)
        layout.setContentsMargins(20, 20, 20, 20)
        
        # Titre
        title = QLabel("📋 Rapport Stock")
        title.setObjectName("reportTitle")
        layout.addWidget(title)
        
        # Contrôles de période
        period_layout = QHBoxLayout()
        
        period_layout.addWidget(QLabel("Période:"))
        
        self.start_date = QDateEdit()
        self.start_date.setDate(QDate.currentDate().addDays(-30))
        self.start_date.setDisplayFormat("dd/MM/yyyy")
        period_layout.addWidget(self.start_date)
        
        period_layout.addWidget(QLabel("à"))
        
        self.end_date = QDateEdit()
        self.end_date.setDate(QDate.currentDate())
        self.end_date.setDisplayFormat("dd/MM/yyyy")
        period_layout.addWidget(self.end_date)
        
        refresh_btn = QPushButton("🔄 Actualiser")
        refresh_btn.setObjectName("actionButton")
        refresh_btn.clicked.connect(self.refresh_stock_report)
        period_layout.addWidget(refresh_btn)
        
        period_layout.addStretch()
        layout.addLayout(period_layout)
        
        # Tableau des mouvements
        self.stock_movements_table = QTableWidget()
        self.stock_movements_table.setObjectName("statsTable")
        layout.addWidget(self.stock_movements_table)
        
        self.tabs.addTab(stock_widget, "Mouvements Stock")
    
    def create_summary_card(self, icon, title, value, subtitle):
        """Crée une carte de résumé"""
        card = QFrame()
        card.setObjectName("summaryCard")
        card.setFixedHeight(100)
        
        layout = QVBoxLayout(card)
        layout.setContentsMargins(15, 10, 15, 10)
        
        # En-tête
        header_layout = QHBoxLayout()
        
        icon_label = QLabel(icon)
        icon_label.setObjectName("summaryIcon")
        header_layout.addWidget(icon_label)
        
        title_label = QLabel(title)
        title_label.setObjectName("summaryTitle")
        header_layout.addWidget(title_label)
        
        header_layout.addStretch()
        layout.addLayout(header_layout)
        
        # Valeur
        value_label = QLabel(value)
        value_label.setObjectName("summaryValue")
        layout.addWidget(value_label)
        
        # Sous-titre
        subtitle_label = QLabel(subtitle)
        subtitle_label.setObjectName("summarySubtitle")
        layout.addWidget(subtitle_label)
        
        return card
    
    def load_report_data(self):
        """Charge les données pour les rapports"""
        try:
            with db_manager.get_session() as session:
                # Statistiques générales
                total_clients = session.query(Client).count()
                active_clients = session.query(Client).filter(Client.is_active == True).count()
                
                total_products = session.query(Product).count()
                products_in_stock = session.query(Product).filter(
                    Product.track_stock == True,
                    Product.current_stock > 0
                ).count()
                
                # Mouvements récents
                week_ago = datetime.now() - timedelta(days=7)
                recent_movements = session.query(StockMovement).filter(
                    StockMovement.date >= week_ago
                ).count()
                
                # Valeur du stock
                products = session.query(Product).filter(
                    Product.track_stock == True,
                    Product.current_stock > 0
                ).all()
                
                total_value = 0
                for product in products:
                    if product.purchase_price and product.current_stock > 0:
                        total_value += float(product.purchase_price) * product.current_stock
                
                # Mettre à jour les cartes
                self.update_summary_card(self.clients_summary_card, str(total_clients), f"{active_clients} actifs")
                self.update_summary_card(self.products_summary_card, str(total_products), f"{products_in_stock} en stock")
                self.update_summary_card(self.movements_summary_card, str(recent_movements), "7 derniers jours")
                self.update_summary_card(self.value_summary_card, f"{total_value:,.0f} DA", "Prix d'achat")
                
                # Charger les rapports détaillés
                self.load_clients_report(session)
                self.load_products_report(session)
                self.load_stock_report(session)
                self.load_alerts(session)
                
                self.show_message("Rapports actualisés", "success")
                
        except Exception as e:
            self.logger.error(f"Erreur lors du chargement des rapports: {e}")
            self.show_message("Erreur lors du chargement", "error")
    
    def update_summary_card(self, card, value, subtitle):
        """Met à jour une carte de résumé"""
        for child in card.findChildren(QLabel):
            if child.objectName() == "summaryValue":
                child.setText(value)
            elif child.objectName() == "summarySubtitle":
                child.setText(subtitle)
    
    def load_clients_report(self, session):
        """Charge le rapport clients"""
        # Configuration du tableau
        headers = ["Type", "Nombre", "Pourcentage"]
        self.clients_stats_table.setColumnCount(len(headers))
        self.clients_stats_table.setHorizontalHeaderLabels(headers)
        
        # Données
        total_clients = session.query(Client).count()
        if total_clients > 0:
            stats_data = [
                ("Total clients", total_clients, "100%"),
                ("Clients actifs", session.query(Client).filter(Client.is_active == True).count(), ""),
                ("Clients inactifs", session.query(Client).filter(Client.is_active == False).count(), ""),
                ("Clients VIP", session.query(Client).filter(Client.is_vip == True).count(), ""),
                ("Particuliers", session.query(Client).filter(Client.client_type == 'individual').count(), ""),
                ("Entreprises", session.query(Client).filter(Client.client_type == 'company').count(), ""),
            ]
            
            # Calculer les pourcentages
            for i, (label, count, _) in enumerate(stats_data[1:], 1):
                percentage = f"{(count / total_clients * 100):.1f}%" if total_clients > 0 else "0%"
                stats_data[i] = (label, count, percentage)
        else:
            stats_data = [("Aucun client", 0, "0%")]
        
        # Remplir le tableau
        self.clients_stats_table.setRowCount(len(stats_data))
        for row, (label, count, percentage) in enumerate(stats_data):
            self.clients_stats_table.setItem(row, 0, QTableWidgetItem(label))
            self.clients_stats_table.setItem(row, 1, QTableWidgetItem(str(count)))
            self.clients_stats_table.setItem(row, 2, QTableWidgetItem(percentage))
        
        # Ajuster les colonnes
        self.clients_stats_table.resizeColumnsToContents()
    
    def load_products_report(self, session):
        """Charge le rapport produits"""
        # Configuration du tableau
        headers = ["Catégorie", "Nombre", "En Stock", "Stock Bas", "Ruptures"]
        self.products_stats_table.setColumnCount(len(headers))
        self.products_stats_table.setHorizontalHeaderLabels(headers)
        
        # Données par catégorie
        from src.dal.models.product import Category
        categories = session.query(Category).all()
        
        stats_data = []
        for category in categories:
            products_in_cat = session.query(Product).filter(Product.category_id == category.id).count()
            in_stock = session.query(Product).filter(
                Product.category_id == category.id,
                Product.track_stock == True,
                Product.current_stock > 0
            ).count()
            low_stock = session.query(Product).filter(
                Product.category_id == category.id,
                Product.track_stock == True,
                Product.current_stock <= Product.min_stock,
                Product.current_stock > 0
            ).count()
            out_of_stock = session.query(Product).filter(
                Product.category_id == category.id,
                Product.track_stock == True,
                Product.current_stock <= 0
            ).count()
            
            stats_data.append((category.name, products_in_cat, in_stock, low_stock, out_of_stock))
        
        # Ajouter le total
        total_products = session.query(Product).count()
        total_in_stock = session.query(Product).filter(
            Product.track_stock == True,
            Product.current_stock > 0
        ).count()
        total_low_stock = session.query(Product).filter(
            Product.track_stock == True,
            Product.current_stock <= Product.min_stock,
            Product.current_stock > 0
        ).count()
        total_out_of_stock = session.query(Product).filter(
            Product.track_stock == True,
            Product.current_stock <= 0
        ).count()
        
        stats_data.insert(0, ("TOTAL", total_products, total_in_stock, total_low_stock, total_out_of_stock))
        
        # Remplir le tableau
        self.products_stats_table.setRowCount(len(stats_data))
        for row, (category, total, in_stock, low_stock, out_of_stock) in enumerate(stats_data):
            self.products_stats_table.setItem(row, 0, QTableWidgetItem(category))
            self.products_stats_table.setItem(row, 1, QTableWidgetItem(str(total)))
            self.products_stats_table.setItem(row, 2, QTableWidgetItem(str(in_stock)))
            
            # Colorer les alertes
            low_item = QTableWidgetItem(str(low_stock))
            if low_stock > 0:
                low_item.setForeground(QColor("#ffaa00"))
            self.products_stats_table.setItem(row, 3, low_item)
            
            out_item = QTableWidgetItem(str(out_of_stock))
            if out_of_stock > 0:
                out_item.setForeground(QColor("#ff4444"))
            self.products_stats_table.setItem(row, 4, out_item)
        
        # Ajuster les colonnes
        self.products_stats_table.resizeColumnsToContents()
    
    def load_stock_report(self, session):
        """Charge le rapport des mouvements de stock"""
        # Récupérer la période sélectionnée
        start_date = self.start_date.date().toPyDate()
        end_date = self.end_date.date().toPyDate()
        
        # Configuration du tableau
        headers = ["Date", "Produit", "Type", "Quantité", "Motif", "Référence"]
        self.stock_movements_table.setColumnCount(len(headers))
        self.stock_movements_table.setHorizontalHeaderLabels(headers)
        
        # Récupérer les mouvements
        movements = session.query(StockMovement).filter(
            StockMovement.date >= datetime.combine(start_date, datetime.min.time()),
            StockMovement.date <= datetime.combine(end_date, datetime.max.time())
        ).order_by(StockMovement.date.desc()).limit(100).all()
        
        # Remplir le tableau
        self.stock_movements_table.setRowCount(len(movements))
        for row, movement in enumerate(movements):
            # Date
            date_str = movement.date.strftime("%d/%m/%Y") if movement.date else ""
            self.stock_movements_table.setItem(row, 0, QTableWidgetItem(date_str))
            
            # Produit
            product_name = movement.product.name if movement.product else "Produit supprimé"
            self.stock_movements_table.setItem(row, 1, QTableWidgetItem(product_name))
            
            # Type
            type_text = {
                MovementType.IN: "Entrée",
                MovementType.OUT: "Sortie",
                MovementType.ADJUSTMENT: "Ajustement"
            }.get(movement.movement_type, "Inconnu")
            self.stock_movements_table.setItem(row, 2, QTableWidgetItem(type_text))
            
            # Quantité
            quantity_item = QTableWidgetItem(f"{movement.quantity:+.2f}")
            if movement.quantity > 0:
                quantity_item.setForeground(QColor("#00ff88"))
            else:
                quantity_item.setForeground(QColor("#ff6b6b"))
            self.stock_movements_table.setItem(row, 3, quantity_item)
            
            # Motif
            self.stock_movements_table.setItem(row, 4, QTableWidgetItem(str(movement.reason.value if movement.reason else "")))
            
            # Référence
            self.stock_movements_table.setItem(row, 5, QTableWidgetItem(movement.reference or ""))
        
        # Ajuster les colonnes
        self.stock_movements_table.resizeColumnsToContents()
    
    def load_alerts(self, session):
        """Charge les alertes"""
        self.alerts_list.clear()
        
        # Produits en stock bas
        low_stock_products = session.query(Product).filter(
            Product.track_stock == True,
            Product.current_stock <= Product.min_stock,
            Product.current_stock > 0
        ).all()
        
        for product in low_stock_products:
            alert_text = f"⚠️ Stock bas: {product.name} ({product.current_stock:.0f} restant)"
            item = QListWidgetItem(alert_text)
            item.setForeground(QColor("#ffaa00"))
            self.alerts_list.addItem(item)
        
        # Produits en rupture
        out_of_stock_products = session.query(Product).filter(
            Product.track_stock == True,
            Product.current_stock <= 0
        ).all()
        
        for product in out_of_stock_products:
            alert_text = f"❌ Rupture de stock: {product.name}"
            item = QListWidgetItem(alert_text)
            item.setForeground(QColor("#ff4444"))
            self.alerts_list.addItem(item)
        
        # Message si pas d'alertes
        if self.alerts_list.count() == 0:
            item = QListWidgetItem("✅ Aucune alerte - Tous les stocks sont OK")
            item.setForeground(QColor("#00ff88"))
            self.alerts_list.addItem(item)
    
    def refresh_stock_report(self):
        """Actualise le rapport de stock"""
        try:
            with db_manager.get_session() as session:
                self.load_stock_report(session)
            self.show_message("Rapport de stock actualisé", "success")
        except Exception as e:
            self.logger.error(f"Erreur lors de l'actualisation: {e}")
            self.show_message("Erreur lors de l'actualisation", "error")
    
    def handle_action(self, action):
        """Gère les actions du module"""
        if action == "new":
            QMessageBox.information(self, "Rapports", "Génération de nouveaux rapports en cours de développement")
        else:
            super().handle_action(action)
    
    def apply_styles(self):
        """Applique les styles CSS"""
        self.setStyleSheet("""
            #reportTabs {
                background: transparent;
                border: none;
            }
            
            #reportTabs::pane {
                border: 1px solid rgba(255, 255, 255, 0.1);
                border-radius: 8px;
                background: rgba(255, 255, 255, 0.02);
            }
            
            #reportTabs::tab-bar {
                alignment: left;
            }
            
            QTabBar::tab {
                background: rgba(255, 255, 255, 0.05);
                border: 1px solid rgba(255, 255, 255, 0.1);
                padding: 10px 20px;
                margin-right: 2px;
                border-top-left-radius: 6px;
                border-top-right-radius: 6px;
                color: rgba(255, 255, 255, 0.8);
            }
            
            QTabBar::tab:selected {
                background: rgba(0, 212, 255, 0.2);
                border-color: #00d4ff;
                color: #00d4ff;
            }
            
            QTabBar::tab:hover {
                background: rgba(255, 255, 255, 0.1);
            }
            
            #reportTitle {
                font-size: 20px;
                font-weight: bold;
                color: #00d4ff;
                margin-bottom: 15px;
            }
            
            #summaryCard {
                background: rgba(255, 255, 255, 0.05);
                border: 1px solid rgba(255, 255, 255, 0.1);
                border-radius: 8px;
                margin: 5px;
            }
            
            #summaryCard:hover {
                background: rgba(255, 255, 255, 0.08);
                border-color: rgba(0, 212, 255, 0.3);
            }
            
            #summaryIcon {
                font-size: 18px;
                color: #00d4ff;
            }
            
            #summaryTitle {
                font-size: 12px;
                color: rgba(255, 255, 255, 0.8);
                font-weight: 500;
            }
            
            #summaryValue {
                font-size: 18px;
                font-weight: bold;
                color: #ffffff;
                margin: 5px 0;
            }
            
            #summarySubtitle {
                font-size: 10px;
                color: rgba(255, 255, 255, 0.6);
            }
            
            #chartFrame, #alertsFrame {
                background: rgba(255, 255, 255, 0.03);
                border: 1px solid rgba(255, 255, 255, 0.1);
                border-radius: 8px;
                margin: 5px;
            }
            
            #chartTitle, #alertsTitle {
                font-size: 14px;
                font-weight: bold;
                color: #00d4ff;
                margin-bottom: 10px;
            }
            
            #chartPlaceholder {
                color: rgba(255, 255, 255, 0.6);
                font-style: italic;
            }
            
            #alertsList {
                background: transparent;
                border: none;
                color: rgba(255, 255, 255, 0.9);
            }
            
            #alertsList::item {
                padding: 8px;
                border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            }
            
            #alertsList::item:hover {
                background: rgba(0, 212, 255, 0.1);
            }
            
            #statsTable {
                background: rgba(255, 255, 255, 0.05);
                border: 1px solid rgba(255, 255, 255, 0.1);
                border-radius: 8px;
                gridline-color: rgba(255, 255, 255, 0.1);
                selection-background-color: rgba(0, 212, 255, 0.2);
            }
            
            #statsTable::item {
                padding: 8px;
                border-bottom: 1px solid rgba(255, 255, 255, 0.05);
            }
            
            #statsTable::item:selected {
                background: rgba(0, 212, 255, 0.2);
            }
            
            QHeaderView::section {
                background: rgba(0, 0, 0, 0.3);
                border: none;
                border-bottom: 1px solid rgba(255, 255, 255, 0.1);
                padding: 10px;
                font-weight: bold;
                color: #00d4ff;
            }
        """)

    def showEvent(self, event):
        """Événement d'affichage"""
        super().showEvent(event)
        self.apply_styles()
