# 📖 Guide d'Utilisation GSCOM

## 🚀 Démarrage Rapide

### 1. Installation et Configuration

```bash
# 1. <PERSON><PERSON><PERSON> ou télécharger le projet
cd GSCOM

# 2. Installer les dépendances essentielles
pip install PyQt5 SQLAlchemy bcrypt

# 3. Initialiser la base de données
python simple_init.py

# 4. Lancer l'application
python main.py
```

### 2. Première Connexion

- **Utilisateur :** `admin`
- **Mot de passe :** `admin123`

## 🏢 Interface Principale

### Navigation Latérale

L'interface principale dispose d'une barre de navigation latérale avec les modules suivants :

- **📊 Tableau de bord** - Vue d'ensemble et statistiques
- **💼 Commercial** - Gestion des ventes et achats
- **👥 Clients** - Base de données clients
- **🏭 Fournisseurs** - Gestion des fournisseurs
- **📦 Produits** - Catalogue produits
- **📋 Stock** - Gestion des stocks
- **📝 Inventaire** - Outils d'inventaire
- **💰 Comptabilité** - Gestion comptable
- **📈 Rapports** - Génération de rapports
- **⚙️ Paramètres** - Configuration

### Zone de Contenu

- **En-tête** : Titre du module actuel + actions rapides
- **Contenu principal** : Interface spécifique au module
- **Barre de statut** : Informations système et utilisateur

## 📊 Modules Détaillés

### Tableau de Bord

**Fonctionnalités :**
- Cartes de statistiques (ventes, clients, stock, commandes)
- Graphiques de performance
- Alertes et notifications
- Raccourcis vers les actions fréquentes

**Utilisation :**
1. Vue d'ensemble immédiate des KPIs
2. Clic sur les cartes pour accéder aux détails
3. Surveillance des alertes en temps réel

### Gestion Commerciale

**Cycle de vente complet :**
1. **Devis** → 2. **Commande** → 3. **Livraison** → 4. **Facture** → 5. **Paiement**

**Fonctionnalités :**
- Création de devis avec calculs automatiques
- Conversion devis → commande
- Génération de bons de livraison
- Facturation avec gestion TVA
- Suivi des paiements et encours

### Gestion des Clients

**Informations stockées :**
- Données personnelles/entreprise
- Coordonnées complètes
- Conditions commerciales
- Historique des transactions
- Encours et limites de crédit

**Actions disponibles :**
- Ajouter/modifier/supprimer clients
- Recherche et filtrage avancés
- Export des données
- Génération d'étiquettes

### Gestion des Produits

**Caractéristiques produits :**
- Code article et code-barres
- Description détaillée
- Prix d'achat et de vente
- Gestion des stocks
- Catégorisation
- Images et fiches techniques

**Fonctionnalités :**
- Calcul automatique des marges
- Gestion des seuils de stock
- Alertes de rupture
- Historique des mouvements

### Gestion des Stocks

**Types de mouvements :**
- **Entrées** : Achats, retours clients, ajustements positifs
- **Sorties** : Ventes, retours fournisseurs, ajustements négatifs
- **Transferts** : Entre emplacements (si multi-sites)

**Fonctionnalités :**
- Suivi en temps réel
- Valorisation du stock (FIFO, LIFO, PMP)
- Alertes automatiques
- Traçabilité complète

### Inventaire

**Processus d'inventaire :**
1. **Planification** : Définir la portée et la date
2. **Préparation** : Édition des fiches de comptage
3. **Comptage** : Saisie des quantités réelles
4. **Réconciliation** : Analyse des écarts
5. **Validation** : Application des ajustements

### Comptabilité

**Plan comptable :**
- Comptes structurés par classe
- Écritures automatiques depuis les ventes
- Journaux spécialisés (ventes, achats, banque, caisse)
- États comptables (balance, grand livre)

**Intégration :**
- Génération automatique d'écritures depuis les factures
- Lettrage des comptes clients/fournisseurs
- Calcul automatique de la TVA

## 🔧 Configuration et Paramètres

### Paramètres Entreprise

**Informations légales :**
- Raison sociale et forme juridique
- Adresse complète
- Numéros d'identification (NIF, RC)
- Logo et signature

**Paramètres commerciaux :**
- Devise et taux de TVA
- Conditions de paiement par défaut
- Numérotation des documents
- Modèles d'impression

### Gestion des Utilisateurs

**Rôles prédéfinis :**
- **Administrateur** : Accès complet
- **Gestionnaire** : Modules commerciaux et stocks
- **Comptable** : Module comptabilité
- **Vendeur** : Consultation et saisie limitée

**Permissions granulaires :**
- Lecture/Écriture par module
- Validation des documents
- Accès aux rapports
- Administration système

### Sauvegardes

**Types de sauvegarde :**
- **Automatique** : Quotidienne (configurable)
- **Manuelle** : À la demande
- **Export** : Données spécifiques

**Restauration :**
- Points de restauration datés
- Restauration sélective
- Vérification d'intégrité

## 📈 Rapports et Analyses

### Rapports Commerciaux

- **Chiffre d'affaires** : Par période, client, produit
- **Marges** : Analyse de rentabilité
- **Encours clients** : Suivi des impayés
- **Performance commerciale** : Évolution des ventes

### Rapports Stock

- **Valorisation** : Valeur du stock par catégorie
- **Rotation** : Analyse des mouvements
- **Ruptures** : Produits en rupture ou stock bas
- **Inventaire** : États d'inventaire et écarts

### Rapports Comptables

- **Balance** : Soldes des comptes
- **Grand livre** : Détail des écritures
- **Journaux** : Chronologie des opérations
- **TVA** : Déclarations et calculs

## 🔍 Recherche et Filtrage

### Recherche Globale

- Barre de recherche dans l'en-tête
- Recherche dans tous les modules
- Résultats groupés par type
- Accès direct aux fiches

### Filtres Avancés

- **Par dates** : Périodes personnalisées
- **Par statut** : Actif/inactif, payé/impayé
- **Par catégorie** : Classification des données
- **Par montant** : Tranches de valeurs

## 📱 Raccourcis Clavier

### Navigation
- `Ctrl + 1-9` : Accès direct aux modules
- `Ctrl + F` : Recherche
- `Ctrl + R` : Actualiser
- `F5` : Actualiser les données

### Actions
- `Ctrl + N` : Nouveau document
- `Ctrl + S` : Sauvegarder
- `Ctrl + P` : Imprimer
- `Ctrl + E` : Exporter
- `Escape` : Annuler/Fermer

### Système
- `Ctrl + Q` : Quitter
- `F1` : Aide
- `Ctrl + ,` : Préférences

## 🚨 Résolution de Problèmes

### Problèmes Courants

**Connexion impossible :**
1. Vérifier les identifiants
2. Contrôler le verrouillage du compte
3. Réinitialiser le mot de passe (admin)

**Erreur de base de données :**
1. Vérifier l'espace disque
2. Relancer l'initialisation : `python simple_init.py`
3. Restaurer une sauvegarde

**Interface qui ne répond pas :**
1. Fermer et relancer l'application
2. Vérifier les ressources système
3. Redémarrer en mode sans échec

**Données manquantes :**
1. Vérifier les filtres actifs
2. Contrôler les permissions utilisateur
3. Consulter les logs d'erreur

### Logs et Diagnostic

**Emplacement des logs :**
- Windows : `C:\Users\<USER>\.gscom\logs\`
- Linux/Mac : `~/.gscom/logs/`

**Types de logs :**
- `gscom_YYYYMMDD.log` : Log principal
- Rotation automatique (5 fichiers max)
- Niveaux : INFO, WARNING, ERROR, CRITICAL

### Support Technique

**Avant de contacter le support :**
1. Noter le message d'erreur exact
2. Reproduire le problème
3. Consulter les logs récents
4. Préparer les informations système

**Informations à fournir :**
- Version de GSCOM
- Système d'exploitation
- Message d'erreur complet
- Étapes pour reproduire
- Logs d'erreur

## 📚 Ressources Supplémentaires

### Documentation Technique
- `README.md` : Installation et configuration
- Code source commenté
- Architecture détaillée

### Formation
- Tutoriels vidéo (à venir)
- Webinaires de formation
- Documentation utilisateur avancée

### Communauté
- Forum utilisateurs
- Base de connaissances
- Suggestions d'amélioration

---

**GSCOM** - *Votre solution complète de gestion d'entreprise* 🚀

*Pour toute question, consultez la documentation technique ou contactez le support.*
