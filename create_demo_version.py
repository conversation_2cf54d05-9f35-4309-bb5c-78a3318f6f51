#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Script pour créer une version de démonstration complète de GSCOM
Ajoute toutes les données nécessaires pour une démonstration
"""

import sys
import os
from datetime import datetime, timedelta

# Ajouter le répertoire src au path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from src.dal.database import db_manager
from src.dal.models.product import Product, Category, Unit, ProductType, ProductStatus
from src.dal.models.client import Client, Supplier, ClientType, PaymentTerms
from src.dal.models.stock import StockMovement, MovementType, MovementReason
from src.dal.models.user import User

def create_additional_products():
    """Crée des produits supplémentaires pour la démo"""
    print("📦 Création de produits supplémentaires...")
    
    try:
        with db_manager.get_session() as session:
            # Récupérer les catégories et unités
            categories = {cat.code: cat.id for cat in session.query(Category).all()}
            units = {unit.code: unit.id for unit in session.query(Unit).all()}
            
            additional_products = [
                {
                    "code": "PRD007",
                    "name": "Imprimante Laser HP",
                    "description": "Imprimante laser HP LaserJet Pro, noir et blanc",
                    "category_id": categories.get("CAT001"),  # Électronique
                    "unit_id": units.get("PC"),
                    "product_type": ProductType.PRODUCT,
                    "status": ProductStatus.ACTIVE,
                    "purchase_price": 35000.00,
                    "sale_price": 48000.00,
                    "tax_rate": 19.0,
                    "track_stock": True,
                    "current_stock": 12,
                    "min_stock": 3,
                    "max_stock": 25
                },
                {
                    "code": "PRD008",
                    "name": "Clavier Sans Fil",
                    "description": "Clavier sans fil ergonomique avec pavé numérique",
                    "category_id": categories.get("CAT001"),  # Électronique
                    "unit_id": units.get("PC"),
                    "product_type": ProductType.PRODUCT,
                    "status": ProductStatus.ACTIVE,
                    "purchase_price": 3500.00,
                    "sale_price": 5200.00,
                    "tax_rate": 19.0,
                    "track_stock": True,
                    "current_stock": 25,
                    "min_stock": 10,
                    "max_stock": 50
                },
                {
                    "code": "PRD009",
                    "name": "Armoire Métallique",
                    "description": "Armoire métallique 2 portes pour bureau",
                    "category_id": categories.get("CAT002"),  # Mobilier
                    "unit_id": units.get("PC"),
                    "product_type": ProductType.PRODUCT,
                    "status": ProductStatus.ACTIVE,
                    "purchase_price": 18000.00,
                    "sale_price": 26000.00,
                    "tax_rate": 19.0,
                    "track_stock": True,
                    "current_stock": 6,
                    "min_stock": 2,
                    "max_stock": 15
                },
                {
                    "code": "PRD010",
                    "name": "Cartouche d'Encre",
                    "description": "Cartouche d'encre noire compatible HP",
                    "category_id": categories.get("CAT003"),  # Fournitures
                    "unit_id": units.get("PC"),
                    "product_type": ProductType.CONSUMABLE,
                    "status": ProductStatus.ACTIVE,
                    "purchase_price": 2800.00,
                    "sale_price": 4200.00,
                    "tax_rate": 19.0,
                    "track_stock": True,
                    "current_stock": 45,
                    "min_stock": 20,
                    "max_stock": 100
                },
                {
                    "code": "SRV001",
                    "name": "Formation Informatique",
                    "description": "Formation en informatique de base (8 heures)",
                    "category_id": categories.get("CAT001"),  # Électronique
                    "unit_id": units.get("PC"),  # Heure
                    "product_type": ProductType.SERVICE,
                    "status": ProductStatus.ACTIVE,
                    "purchase_price": 0.00,
                    "sale_price": 8000.00,
                    "tax_rate": 19.0,
                    "track_stock": False,
                    "current_stock": 0,
                    "min_stock": 0,
                    "max_stock": 0
                }
            ]
            
            created_count = 0
            for product_data in additional_products:
                # Vérifier si le produit existe déjà
                existing = session.query(Product).filter(Product.code == product_data["code"]).first()
                if not existing:
                    product = Product(**product_data)
                    session.add(product)
                    created_count += 1
            
            session.commit()
            print(f"✅ {created_count} produits supplémentaires créés")
            
    except Exception as e:
        print(f"❌ Erreur lors de la création des produits: {e}")

def create_additional_clients():
    """Crée des clients supplémentaires pour la démo"""
    print("👥 Création de clients supplémentaires...")
    
    try:
        with db_manager.get_session() as session:
            additional_clients = [
                {
                    "code": "CLI006",
                    "name": "Yasmine Benaissa",
                    "client_type": ClientType.INDIVIDUAL,
                    "email": "<EMAIL>",
                    "phone": "+*********** 901",
                    "city": "Tlemcen",
                    "is_active": True
                },
                {
                    "code": "CLI007",
                    "name": "Rachid Mansouri",
                    "company_name": "Mansouri Électronique SARL",
                    "client_type": ClientType.COMPANY,
                    "email": "<EMAIL>",
                    "phone": "+*********** 567",
                    "city": "Sétif",
                    "tax_id": "***************",
                    "payment_terms": PaymentTerms.NET_30,
                    "credit_limit": 75000.00,
                    "is_active": True,
                    "is_vip": True
                },
                {
                    "code": "CLI008",
                    "name": "Leila Hamidi",
                    "client_type": ClientType.INDIVIDUAL,
                    "email": "<EMAIL>",
                    "phone": "+*********** 123",
                    "city": "Béjaïa",
                    "is_active": True
                },
                {
                    "code": "CLI009",
                    "name": "Directeur Technique",
                    "company_name": "Université d'Alger",
                    "client_type": ClientType.GOVERNMENT,
                    "email": "<EMAIL>",
                    "phone": "+*********** 789",
                    "city": "Alger",
                    "payment_terms": PaymentTerms.NET_90,
                    "is_active": True
                },
                {
                    "code": "CLI010",
                    "name": "Omar Belkacem",
                    "company_name": "Belkacem Trading",
                    "client_type": ClientType.COMPANY,
                    "email": "<EMAIL>",
                    "phone": "+*********** 345",
                    "city": "Ouargla",
                    "tax_id": "555666777888999",
                    "payment_terms": PaymentTerms.NET_60,
                    "credit_limit": 40000.00,
                    "is_active": True
                }
            ]
            
            created_count = 0
            for client_data in additional_clients:
                # Vérifier si le client existe déjà
                existing = session.query(Client).filter(Client.code == client_data["code"]).first()
                if not existing:
                    client = Client(**client_data)
                    session.add(client)
                    created_count += 1
            
            session.commit()
            print(f"✅ {created_count} clients supplémentaires créés")
            
    except Exception as e:
        print(f"❌ Erreur lors de la création des clients: {e}")

def create_realistic_stock_movements():
    """Crée des mouvements de stock réalistes"""
    print("📊 Création de mouvements de stock réalistes...")
    
    try:
        with db_manager.get_session() as session:
            # Récupérer les produits et utilisateurs
            products = session.query(Product).filter(Product.track_stock == True).all()
            admin_user = session.query(User).filter(User.username == 'admin').first()
            
            if not products or not admin_user:
                print("❌ Données manquantes pour créer les mouvements")
                return
            
            movements_data = []
            
            # Créer des mouvements sur les 3 derniers mois
            base_date = datetime.now() - timedelta(days=90)
            
            for i, product in enumerate(products):
                # Entrées initiales (il y a 3 mois)
                initial_stock = 100 + (i * 20)
                movements_data.append({
                    'product_id': product.id,
                    'movement_type': MovementType.IN,
                    'quantity': initial_stock,
                    'reason': MovementReason.PURCHASE,
                    'reference': f"ACH{2024000 + i:03d}",
                    'notes': f"Stock initial - {product.name}",
                    'date': base_date + timedelta(days=i),
                    'user_id': admin_user.id
                })
                
                # Ventes régulières
                for week in range(12):  # 12 semaines
                    if week % 2 == 0:  # Ventes toutes les 2 semaines
                        sale_qty = -(5 + (i % 3))
                        movements_data.append({
                            'product_id': product.id,
                            'movement_type': MovementType.OUT,
                            'quantity': sale_qty,
                            'reason': MovementReason.SALE,
                            'reference': f"VTE{2024000 + (week * 10) + i:03d}",
                            'notes': f"Vente - {product.name}",
                            'date': base_date + timedelta(weeks=week, days=i % 7),
                            'user_id': admin_user.id
                        })
                
                # Réapprovisionnements
                if i % 3 == 0:  # Réappro pour 1 produit sur 3
                    restock_qty = 50 + (i * 5)
                    movements_data.append({
                        'product_id': product.id,
                        'movement_type': MovementType.IN,
                        'quantity': restock_qty,
                        'reason': MovementReason.PURCHASE,
                        'reference': f"ACH{2024100 + i:03d}",
                        'notes': f"Réapprovisionnement - {product.name}",
                        'date': base_date + timedelta(days=45 + i),
                        'user_id': admin_user.id
                    })
                
                # Quelques retours clients
                if i % 4 == 0:  # Retours pour 1 produit sur 4
                    movements_data.append({
                        'product_id': product.id,
                        'movement_type': MovementType.IN,
                        'quantity': 2,
                        'reason': MovementReason.RETURN_CUSTOMER,
                        'reference': f"RET{2024000 + i:03d}",
                        'notes': f"Retour client - {product.name}",
                        'date': base_date + timedelta(days=60 + i),
                        'user_id': admin_user.id
                    })
                
                # Ajustements d'inventaire
                if i % 5 == 0:  # Ajustements pour 1 produit sur 5
                    adj_qty = -1 if i % 2 == 0 else 2
                    movements_data.append({
                        'product_id': product.id,
                        'movement_type': MovementType.ADJUSTMENT,
                        'quantity': adj_qty,
                        'reason': MovementReason.INVENTORY,
                        'reference': f"INV{2024000 + i:03d}",
                        'notes': f"Ajustement inventaire - {product.name}",
                        'date': base_date + timedelta(days=75 + i),
                        'user_id': admin_user.id
                    })
            
            # Créer les mouvements
            created_count = 0
            for movement_data in movements_data:
                # Vérifier si le mouvement existe déjà
                existing = session.query(StockMovement).filter(
                    StockMovement.product_id == movement_data['product_id'],
                    StockMovement.reference == movement_data['reference']
                ).first()
                
                if not existing:
                    movement = StockMovement(**movement_data)
                    session.add(movement)
                    created_count += 1
            
            session.commit()
            print(f"✅ {created_count} mouvements de stock créés")
            
            # Recalculer les stocks actuels
            print("🔄 Recalcul des stocks...")
            for product in products:
                total_movements = session.query(StockMovement).filter(
                    StockMovement.product_id == product.id
                ).all()
                
                new_stock = sum(mov.quantity for mov in total_movements)
                product.current_stock = max(0, new_stock)  # Éviter les stocks négatifs
            
            session.commit()
            print("✅ Stocks recalculés")
            
    except Exception as e:
        print(f"❌ Erreur lors de la création des mouvements: {e}")
        import traceback
        traceback.print_exc()

def show_demo_statistics():
    """Affiche les statistiques de la version démo"""
    print("\n📈 Statistiques de la version démo :")
    
    try:
        with db_manager.get_session() as session:
            # Compter les enregistrements
            clients_count = session.query(Client).count()
            suppliers_count = session.query(Supplier).count()
            products_count = session.query(Product).count()
            movements_count = session.query(StockMovement).count()
            categories_count = session.query(Category).count()
            units_count = session.query(Unit).count()
            
            print(f"   👥 Clients: {clients_count}")
            print(f"   🏭 Fournisseurs: {suppliers_count}")
            print(f"   📦 Produits: {products_count}")
            print(f"   🔄 Mouvements de stock: {movements_count}")
            print(f"   📂 Catégories: {categories_count}")
            print(f"   📏 Unités: {units_count}")
            
            # Statistiques avancées
            active_clients = session.query(Client).filter(Client.is_active == True).count()
            vip_clients = session.query(Client).filter(Client.is_vip == True).count()
            
            products_in_stock = session.query(Product).filter(
                Product.track_stock == True,
                Product.current_stock > 0
            ).count()
            
            low_stock = session.query(Product).filter(
                Product.track_stock == True,
                Product.current_stock <= Product.min_stock,
                Product.current_stock > 0
            ).count()
            
            out_of_stock = session.query(Product).filter(
                Product.track_stock == True,
                Product.current_stock <= 0
            ).count()
            
            print(f"\n   📊 Détails :")
            print(f"   • Clients actifs: {active_clients}")
            print(f"   • Clients VIP: {vip_clients}")
            print(f"   • Produits en stock: {products_in_stock}")
            print(f"   • Alertes stock bas: {low_stock}")
            print(f"   • Ruptures de stock: {out_of_stock}")
            
            # Valeur du stock
            products = session.query(Product).filter(
                Product.track_stock == True,
                Product.current_stock > 0
            ).all()
            
            total_purchase_value = 0
            total_sale_value = 0
            
            for product in products:
                if product.current_stock > 0:
                    if product.purchase_price:
                        total_purchase_value += product.current_stock * float(product.purchase_price)
                    if product.sale_price:
                        total_sale_value += product.current_stock * float(product.sale_price)
            
            print(f"\n   💰 Valeur du stock :")
            print(f"   • Prix d'achat: {total_purchase_value:,.0f} DA")
            print(f"   • Prix de vente: {total_sale_value:,.0f} DA")
            print(f"   • Marge potentielle: {total_sale_value - total_purchase_value:,.0f} DA")
            
    except Exception as e:
        print(f"❌ Erreur lors du calcul des statistiques: {e}")

def main():
    """Fonction principale"""
    print("🎬 Création de la version de démonstration GSCOM...")
    print("=" * 60)
    
    try:
        # Créer des données supplémentaires
        create_additional_products()
        create_additional_clients()
        create_realistic_stock_movements()
        
        # Afficher les statistiques
        show_demo_statistics()
        
        print("\n" + "=" * 60)
        print("🎉 VERSION DE DÉMONSTRATION CRÉÉE AVEC SUCCÈS !")
        print("=" * 60)
        
        print("\n🚀 Fonctionnalités de démonstration disponibles :")
        print("   ✅ Base de données complète avec données réalistes")
        print("   ✅ Historique de mouvements sur 3 mois")
        print("   ✅ Clients variés (particuliers, entreprises, administration)")
        print("   ✅ Produits avec gestion des stocks et alertes")
        print("   ✅ Rapports et analyses détaillés")
        print("   ✅ Tableau de bord avec statistiques en temps réel")
        
        print("\n📋 Modules opérationnels :")
        print("   • 📊 Tableau de bord - Statistiques et KPIs")
        print("   • 👥 Gestion des clients - CRUD complet")
        print("   • 📦 Gestion des produits - Catalogue avec stocks")
        print("   • 📋 Gestion des stocks - Mouvements et alertes")
        print("   • 📈 Rapports - Analyses et statistiques")
        print("   • ⚙️ Paramètres - Configuration utilisateur")
        
        print("\n🎯 Pour tester la démonstration :")
        print("   python main.py")
        print("   Utilisateur: admin")
        print("   Mot de passe: admin123")
        
        print("\n💡 Scénarios de test suggérés :")
        print("   1. Explorer le tableau de bord et les statistiques")
        print("   2. Naviguer dans la liste des clients et produits")
        print("   3. Créer de nouveaux clients et produits")
        print("   4. Effectuer des mouvements de stock")
        print("   5. Consulter les rapports et analyses")
        print("   6. Tester les alertes de stock bas")
        
    except Exception as e:
        print(f"\n❌ Erreur: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
