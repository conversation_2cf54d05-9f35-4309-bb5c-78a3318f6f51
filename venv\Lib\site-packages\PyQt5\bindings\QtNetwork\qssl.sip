// qssl.sip generated by MetaSIP
//
// This file is part of the QtNetwork Python extension module.
//
// Copyright (c) 2024 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt5.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


%If (PyQt_SSL)

namespace QSsl
{
%TypeHeaderCode
#include <qssl.h>
%End

    enum KeyType
    {
        PrivateKey,
        PublicKey,
    };

    enum EncodingFormat
    {
        <PERSON>em,
        Der,
    };

    enum KeyAlgorithm
    {
        Opaque,
        Rsa,
        Dsa,
%If (Qt_5_5_0 -)
        Ec,
%End
%If (Qt_5_13_0 -)
        Dh,
%End
    };

    enum AlternativeNameEntryType
    {
        EmailEntry,
        DnsEntry,
%If (Qt_5_13_0 -)
        IpAddressEntry,
%End
    };

    enum SslProtocol
    {
        UnknownProtocol,
        SslV3,
        SslV2,
        TlsV1_0,
%If (Qt_5_5_0 -)
        TlsV1_0OrLater,
%End
        TlsV1_1,
%If (Qt_5_5_0 -)
        TlsV1_1OrLater,
%End
        TlsV1_2,
%If (Qt_5_5_0 -)
        TlsV1_2OrLater,
%End
        AnyProtocol,
        TlsV1SslV3,
        SecureProtocols,
%If (Qt_5_12_0 -)
        DtlsV1_0,
%End
%If (Qt_5_12_0 -)
        DtlsV1_0OrLater,
%End
%If (Qt_5_12_0 -)
        DtlsV1_2,
%End
%If (Qt_5_12_0 -)
        DtlsV1_2OrLater,
%End
%If (Qt_5_12_0 -)
        TlsV1_3,
%End
%If (Qt_5_12_0 -)
        TlsV1_3OrLater,
%End
    };

    enum SslOption
    {
        SslOptionDisableEmptyFragments,
        SslOptionDisableSessionTickets,
        SslOptionDisableCompression,
        SslOptionDisableServerNameIndication,
        SslOptionDisableLegacyRenegotiation,
%If (Qt_5_2_0 -)
        SslOptionDisableSessionSharing,
%End
%If (Qt_5_2_0 -)
        SslOptionDisableSessionPersistence,
%End
%If (Qt_5_6_0 -)
        SslOptionDisableServerCipherPreference,
%End
    };

    typedef QFlags<QSsl::SslOption> SslOptions;
};

%End
%If (PyQt_SSL)
QFlags<QSsl::SslOption> operator|(QSsl::SslOption f1, QFlags<QSsl::SslOption> f2);
%End
