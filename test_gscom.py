#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Tests unitaires pour GSCOM
"""

import sys
import os
import unittest
from unittest.mock import patch, MagicMock

# Ajouter le répertoire src au path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

class TestGSCOMCore(unittest.TestCase):
    """Tests pour les fonctionnalités de base"""
    
    def setUp(self):
        """Configuration des tests"""
        self.test_data = {
            'username': 'test_user',
            'email': '<EMAIL>',
            'password': 'test123',
            'first_name': 'Test',
            'last_name': 'User'
        }
    
    def test_config_loading(self):
        """Test du chargement de la configuration"""
        try:
            from src.core.config import AppConfig
            config = AppConfig()
            
            # Vérifier les valeurs par défaut
            self.assertEqual(config.get('ui.theme'), 'dark')
            self.assertEqual(config.get('business.currency'), 'DA')
            self.assertIsNotNone(config.database_url)
            
            print("✅ Test configuration: OK")
        except Exception as e:
            self.fail(f"Erreur test configuration: {e}")
    
    def test_database_connection(self):
        """Test de la connexion à la base de données"""
        try:
            from src.dal.database import db_manager
            
            # Test de connexion
            self.assertTrue(db_manager.test_connection())
            
            print("✅ Test connexion DB: OK")
        except Exception as e:
            self.fail(f"Erreur test DB: {e}")
    
    def test_user_model(self):
        """Test du modèle utilisateur"""
        try:
            from src.dal.models.user import User
            
            # Créer un utilisateur de test
            user = User(**self.test_data)
            user.set_password('test123')
            
            # Vérifier les propriétés
            self.assertEqual(user.username, 'test_user')
            self.assertEqual(user.full_name, 'Test User')
            self.assertTrue(user.check_password('test123'))
            self.assertFalse(user.check_password('wrong_password'))
            
            print("✅ Test modèle User: OK")
        except Exception as e:
            self.fail(f"Erreur test User: {e}")
    
    def test_user_service(self):
        """Test du service utilisateur"""
        try:
            from src.bll.user_service import UserService
            
            service = UserService()
            
            # Test de validation
            errors = service.validate_data(self.test_data)
            self.assertEqual(len(errors), 0)
            
            # Test de validation avec erreurs
            invalid_data = {'username': 'ab', 'email': 'invalid'}
            errors = service.validate_data(invalid_data)
            self.assertGreater(len(errors), 0)
            
            print("✅ Test service User: OK")
        except Exception as e:
            self.fail(f"Erreur test UserService: {e}")

class TestGSCOMModels(unittest.TestCase):
    """Tests pour les modèles de données"""
    
    def test_client_model(self):
        """Test du modèle client"""
        try:
            from src.dal.models.client import Client, ClientType
            
            client_data = {
                'code': 'CLI001',
                'name': 'Client Test',
                'email': '<EMAIL>',
                'client_type': ClientType.COMPANY
            }
            
            client = Client(**client_data)
            self.assertEqual(client.code, 'CLI001')
            self.assertEqual(client.display_name, 'Client Test')
            
            print("✅ Test modèle Client: OK")
        except Exception as e:
            self.fail(f"Erreur test Client: {e}")
    
    def test_product_model(self):
        """Test du modèle produit"""
        try:
            from src.dal.models.product import Product, ProductType
            
            product_data = {
                'code': 'PRD001',
                'name': 'Produit Test',
                'product_type': ProductType.PRODUCT,
                'sale_price': 100.0,
                'purchase_price': 80.0,
                'tax_rate': 19.0,
                'current_stock': 50.0,
                'min_stock': 10.0
            }
            
            product = Product(**product_data)
            self.assertEqual(product.code, 'PRD001')
            self.assertFalse(product.is_low_stock)
            self.assertFalse(product.is_out_of_stock)
            
            # Test stock bas
            product.current_stock = 5.0
            self.assertTrue(product.is_low_stock)
            
            print("✅ Test modèle Product: OK")
        except Exception as e:
            self.fail(f"Erreur test Product: {e}")

class TestGSCOMUI(unittest.TestCase):
    """Tests pour l'interface utilisateur"""
    
    @patch('PyQt5.QtWidgets.QApplication')
    def test_login_window_creation(self, mock_app):
        """Test de création de la fenêtre de connexion"""
        try:
            from src.ui.login_window import LoginWindow
            
            # Mock de l'application Qt
            mock_app.instance.return_value = MagicMock()
            
            # Créer la fenêtre (sans l'afficher)
            with patch('src.ui.login_window.UserService'):
                window = LoginWindow()
                self.assertIsNotNone(window)
                self.assertEqual(window.windowTitle(), "GSCOM - Connexion")
            
            print("✅ Test LoginWindow: OK")
        except Exception as e:
            self.fail(f"Erreur test LoginWindow: {e}")

class TestGSCOMIntegration(unittest.TestCase):
    """Tests d'intégration"""
    
    def test_full_user_workflow(self):
        """Test du workflow complet utilisateur"""
        try:
            from src.dal.database import db_manager
            from src.bll.user_service import UserService
            from src.dal.models.user import User
            
            service = UserService()
            
            # Test avec une session de base de données
            with db_manager.get_session() as session:
                # Vérifier qu'on peut récupérer des utilisateurs
                users = session.query(User).all()
                self.assertIsInstance(users, list)
                
                # Vérifier l'utilisateur admin
                admin = session.query(User).filter(User.username == 'admin').first()
                if admin:
                    self.assertTrue(admin.is_admin)
                    self.assertTrue(admin.is_active)
            
            print("✅ Test workflow utilisateur: OK")
        except Exception as e:
            self.fail(f"Erreur test workflow: {e}")

def run_tests():
    """Lance tous les tests"""
    print("🧪 Démarrage des tests GSCOM...\n")
    
    # Créer la suite de tests
    test_suite = unittest.TestSuite()
    
    # Ajouter les tests
    test_classes = [
        TestGSCOMCore,
        TestGSCOMModels,
        TestGSCOMUI,
        TestGSCOMIntegration
    ]
    
    for test_class in test_classes:
        tests = unittest.TestLoader().loadTestsFromTestCase(test_class)
        test_suite.addTests(tests)
    
    # Lancer les tests
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(test_suite)
    
    # Résumé
    print("\n" + "="*50)
    print("📊 RÉSUMÉ DES TESTS")
    print("="*50)
    print(f"Tests exécutés: {result.testsRun}")
    print(f"Succès: {result.testsRun - len(result.failures) - len(result.errors)}")
    print(f"Échecs: {len(result.failures)}")
    print(f"Erreurs: {len(result.errors)}")
    
    if result.failures:
        print("\n❌ ÉCHECS:")
        for test, traceback in result.failures:
            print(f"  - {test}: {traceback}")
    
    if result.errors:
        print("\n💥 ERREURS:")
        for test, traceback in result.errors:
            print(f"  - {test}: {traceback}")
    
    success = len(result.failures) == 0 and len(result.errors) == 0
    
    if success:
        print("\n🎉 TOUS LES TESTS SONT PASSÉS !")
    else:
        print("\n⚠️ CERTAINS TESTS ONT ÉCHOUÉ")
    
    return success

if __name__ == "__main__":
    try:
        success = run_tests()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n⚠️ Tests interrompus par l'utilisateur")
        sys.exit(1)
    except Exception as e:
        print(f"\n💥 Erreur inattendue: {e}")
        sys.exit(1)
