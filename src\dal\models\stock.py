#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Modèles pour la gestion des stocks et inventaires
"""

from sqlalchemy import Column, Integer, String, Boolean, DateTime, Text, Numeric, ForeignKey, Enum
from sqlalchemy.sql import func
from sqlalchemy.orm import relationship
import enum
from datetime import datetime

from src.dal.database import Base

class MovementType(enum.Enum):
    """Types de mouvements de stock"""
    IN = "in"                # Entrée
    OUT = "out"              # Sortie
    ADJUSTMENT = "adjustment" # Ajustement
    TRANSFER = "transfer"     # Transfert

class MovementReason(enum.Enum):
    """Raisons des mouvements de stock"""
    PURCHASE = "purchase"     # Achat
    SALE = "sale"            # Vente
    RETURN_CUSTOMER = "return_customer"  # Retour client
    RETURN_SUPPLIER = "return_supplier"  # Retour fournisseur
    LOSS = "loss"            # Perte
    DAMAGE = "damage"        # Dommage
    INVENTORY = "inventory"   # Inventaire
    PRODUCTION = "production" # Production
    TRANSFER = "transfer"     # Transfert
    OTHER = "other"          # Autre

class InventoryStatus(enum.Enum):
    """Statuts d'inventaire"""
    PLANNED = "planned"       # Planifié
    IN_PROGRESS = "in_progress" # En cours
    COMPLETED = "completed"   # Terminé
    CANCELLED = "cancelled"   # Annulé

class AlertType(enum.Enum):
    """Types d'alertes stock"""
    LOW_STOCK = "low_stock"   # Stock bas
    OUT_OF_STOCK = "out_of_stock" # Rupture
    OVERSTOCK = "overstock"   # Surstock
    EXPIRY = "expiry"        # Expiration

class StockMovement(Base):
    """Modèle pour les mouvements de stock"""
    __tablename__ = 'stock_movements'

    id = Column(Integer, primary_key=True)

    # Produit
    product_id = Column(Integer, ForeignKey('products.id'), nullable=False)

    # Type et raison du mouvement
    movement_type = Column(Enum(MovementType), nullable=False)
    reason = Column(Enum(MovementReason), nullable=False)

    # Quantités
    quantity = Column(Numeric(15, 3), nullable=False)
    unit_cost = Column(Numeric(15, 2))

    # Stock avant et après
    stock_before = Column(Numeric(15, 3))
    stock_after = Column(Numeric(15, 3))

    # Références
    reference = Column(String(50))       # Référence du mouvement
    reference_type = Column(String(50))  # Type de document (invoice, order, etc.)
    reference_id = Column(Integer)       # ID du document

    # Date et utilisateur
    date = Column(DateTime, default=func.now())
    user_id = Column(Integer, ForeignKey('users.id'))

    # Notes
    notes = Column(Text)

    # Dates système
    created_at = Column(DateTime, default=func.now())

    # Relations
    product = relationship("Product", back_populates="stock_movements")
    user = relationship("User")

    def __repr__(self):
        return f"<StockMovement(product='{self.product.name if self.product else 'N/A'}', type='{self.movement_type.value}', qty={self.quantity})>"

class Inventory(Base):
    """Modèle pour les inventaires"""
    __tablename__ = 'inventories'

    id = Column(Integer, primary_key=True)
    number = Column(String(50), unique=True, nullable=False)

    # Informations générales
    name = Column(String(100), nullable=False)
    description = Column(Text)

    # Dates
    planned_date = Column(DateTime)
    start_date = Column(DateTime)
    end_date = Column(DateTime)

    # Statut
    status = Column(Enum(InventoryStatus), default=InventoryStatus.PLANNED)

    # Responsable
    responsible_user_id = Column(Integer, ForeignKey('users.id'))

    # Filtres d'inventaire
    category_filter = Column(String(100))  # Catégories à inventorier
    location_filter = Column(String(100))  # Emplacements à inventorier

    # Résultats
    total_products = Column(Integer, default=0)
    counted_products = Column(Integer, default=0)
    discrepancies = Column(Integer, default=0)

    # Notes
    notes = Column(Text)

    # Dates système
    created_at = Column(DateTime, default=func.now())
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now())

    # Relations
    responsible_user = relationship("User")
    lines = relationship("InventoryLine", back_populates="inventory", cascade="all, delete-orphan")

    def __repr__(self):
        return f"<Inventory(number='{self.number}', name='{self.name}', status='{self.status.value}')>"

    @property
    def completion_percentage(self) -> float:
        """Pourcentage de completion de l'inventaire"""
        if self.total_products > 0:
            return (self.counted_products / self.total_products) * 100
        return 0.0

    def start_inventory(self):
        """Démarre l'inventaire"""
        self.status = InventoryStatus.IN_PROGRESS
        self.start_date = datetime.now()

    def complete_inventory(self):
        """Termine l'inventaire"""
        self.status = InventoryStatus.COMPLETED
        self.end_date = datetime.now()

        # Appliquer les ajustements de stock
        for line in self.lines:
            if line.discrepancy != 0:
                # Créer un mouvement de stock pour l'ajustement
                movement = StockMovement(
                    product_id=line.product_id,
                    movement_type=MovementType.ADJUSTMENT,
                    reason=MovementReason.INVENTORY,
                    quantity=abs(line.discrepancy),
                    stock_before=line.system_quantity,
                    stock_after=line.counted_quantity,
                    reference_type='inventory',
                    reference_id=self.id,
                    reference_number=self.number,
                    notes=f"Ajustement inventaire {self.number}"
                )

                # Mettre à jour le stock du produit
                line.product.current_stock = line.counted_quantity

class InventoryLine(Base):
    """Modèle pour les lignes d'inventaire"""
    __tablename__ = 'inventory_lines'

    id = Column(Integer, primary_key=True)
    inventory_id = Column(Integer, ForeignKey('inventories.id'), nullable=False)
    product_id = Column(Integer, ForeignKey('products.id'), nullable=False)

    # Quantités
    system_quantity = Column(Numeric(15, 3), default=0)  # Quantité système
    counted_quantity = Column(Numeric(15, 3))            # Quantité comptée
    discrepancy = Column(Numeric(15, 3), default=0)      # Écart

    # Statut
    is_counted = Column(Boolean, default=False)

    # Détails du comptage
    counted_by = Column(Integer, ForeignKey('users.id'))
    counted_at = Column(DateTime)

    # Notes
    notes = Column(Text)

    # Relations
    inventory = relationship("Inventory", back_populates="lines")
    product = relationship("Product")
    counter = relationship("User")

    def __repr__(self):
        return f"<InventoryLine(product='{self.product.name if self.product else 'N/A'}', system={self.system_quantity}, counted={self.counted_quantity})>"

    def calculate_discrepancy(self):
        """Calcule l'écart entre système et comptage"""
        if self.counted_quantity is not None:
            self.discrepancy = self.counted_quantity - self.system_quantity
        else:
            self.discrepancy = 0

    def mark_as_counted(self, user_id: int, quantity: float):
        """Marque la ligne comme comptée"""
        self.counted_quantity = quantity
        self.counted_by = user_id
        self.counted_at = datetime.now()
        self.is_counted = True
        self.calculate_discrepancy()

class StockAlert(Base):
    """Modèle pour les alertes de stock"""
    __tablename__ = 'stock_alerts'

    id = Column(Integer, primary_key=True)

    # Produit concerné
    product_id = Column(Integer, ForeignKey('products.id'), nullable=False)

    # Type d'alerte
    alert_type = Column(Enum(AlertType), nullable=False)

    # Détails
    message = Column(String(255), nullable=False)
    current_stock = Column(Numeric(15, 3))
    threshold = Column(Numeric(15, 3))

    # Statut
    is_active = Column(Boolean, default=True)
    is_acknowledged = Column(Boolean, default=False)
    acknowledged_by = Column(Integer, ForeignKey('users.id'))
    acknowledged_at = Column(DateTime)

    # Dates
    created_at = Column(DateTime, default=func.now())
    resolved_at = Column(DateTime)

    # Relations
    product = relationship("Product")
    acknowledger = relationship("User")

    def __repr__(self):
        return f"<StockAlert(product='{self.product.name if self.product else 'N/A'}', type='{self.alert_type.value}')>"

    def acknowledge(self, user_id: int):
        """Marque l'alerte comme acquittée"""
        self.is_acknowledged = True
        self.acknowledged_by = user_id
        self.acknowledged_at = datetime.now()

    def resolve(self):
        """Résout l'alerte"""
        self.is_active = False
        self.resolved_at = datetime.now()

    @classmethod
    def create_low_stock_alert(cls, product):
        """Crée une alerte de stock bas"""
        return cls(
            product_id=product.id,
            alert_type=AlertType.LOW_STOCK,
            message=f"Stock bas pour {product.name}: {product.current_stock} (seuil: {product.min_stock})",
            current_stock=product.current_stock,
            threshold=product.min_stock
        )

    @classmethod
    def create_out_of_stock_alert(cls, product):
        """Crée une alerte de rupture de stock"""
        return cls(
            product_id=product.id,
            alert_type=AlertType.OUT_OF_STOCK,
            message=f"Rupture de stock pour {product.name}",
            current_stock=product.current_stock,
            threshold=0
        )
