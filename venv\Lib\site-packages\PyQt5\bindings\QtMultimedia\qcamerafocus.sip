// qcamerafocus.sip generated by MetaSIP
//
// This file is part of the QtMultimedia Python extension module.
//
// Copyright (c) 2024 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt5.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


class QCameraFocusZone
{
%TypeHeaderCode
#include <qcamerafocus.h>
%End

public:
    enum FocusZoneStatus
    {
        Invalid,
        Unused,
        Selected,
        Focused,
    };

    QCameraFocusZone(const QCameraFocusZone &other);
    bool operator==(const QCameraFocusZone &other) const;
    bool operator!=(const QCameraFocusZone &other) const;
    ~QCameraFocusZone();
    bool isValid() const;
    QRectF area() const;
    QCameraFocusZone::FocusZoneStatus status() const;
};

typedef QList<QCameraFocusZone> QCameraFocusZoneList;

class QCameraFocus : public QObject
{
%TypeHeaderCode
#include <qcamerafocus.h>
%End

public:
    enum FocusMode
    {
        ManualFocus,
        HyperfocalFocus,
        InfinityFocus,
        AutoFocus,
        ContinuousFocus,
        MacroFocus,
    };

    typedef QFlags<QCameraFocus::FocusMode> FocusModes;

    enum FocusPointMode
    {
        FocusPointAuto,
        FocusPointCenter,
        FocusPointFaceDetection,
        FocusPointCustom,
    };

    bool isAvailable() const;
    QCameraFocus::FocusModes focusMode() const;
    void setFocusMode(QCameraFocus::FocusModes mode);
    bool isFocusModeSupported(QCameraFocus::FocusModes mode) const;
    QCameraFocus::FocusPointMode focusPointMode() const;
    void setFocusPointMode(QCameraFocus::FocusPointMode mode);
    bool isFocusPointModeSupported(QCameraFocus::FocusPointMode) const;
    QPointF customFocusPoint() const;
    void setCustomFocusPoint(const QPointF &point);
    QCameraFocusZoneList focusZones() const;
    qreal maximumOpticalZoom() const;
    qreal maximumDigitalZoom() const;
    qreal opticalZoom() const;
    qreal digitalZoom() const;
    void zoomTo(qreal opticalZoom, qreal digitalZoom);

signals:
    void opticalZoomChanged(qreal);
    void digitalZoomChanged(qreal);
    void focusZonesChanged();
    void maximumOpticalZoomChanged(qreal);
    void maximumDigitalZoomChanged(qreal);

private:
    QCameraFocus(QCamera *camera);

protected:
%If (Qt_5_14_0 -)
    virtual ~QCameraFocus();
%End

private:
%If (- Qt_5_14_0)
    virtual ~QCameraFocus();
%End
    QCameraFocus(const QCameraFocus &);
};

QFlags<QCameraFocus::FocusMode> operator|(QCameraFocus::FocusMode f1, QFlags<QCameraFocus::FocusMode> f2);
