# 📄 Licence et Crédits GSCOM

## 📋 Licence

**GSCOM - Application de Gestion Commerciale, Stock, Finance, Comptabilité et Inventaire**

© 2024 GSCOM Solutions. Tous droits réservés.

### Conditions d'Utilisation

Cette application est fournie "en l'état" sans garantie d'aucune sorte, expresse ou implicite. L'utilisation de cette application est soumise aux conditions suivantes :

#### ✅ Autorisations

- **Usage Personnel** : Utilisation libre pour des besoins personnels ou de test
- **Usage Éducatif** : Utilisation libre dans un contexte éducatif ou de formation
- **Évaluation** : Test et évaluation de l'application pendant 30 jours
- **Modification** : Adaptation du code pour des besoins spécifiques (usage interne uniquement)

#### ❌ Restrictions

- **Redistribution** : Interdiction de redistribuer l'application sans autorisation
- **Usage Commercial** : Nécessite une licence commerciale
- **Revente** : Interdiction de revendre ou de louer l'application
- **Marque** : Interdiction d'utiliser la marque GSCOM sans autorisation

#### 📞 Licence Commerciale

Pour un usage commercial, veuillez contacter :
- **Email** : <EMAIL>
- **Site Web** : www.gscom.dz
- **Téléphone** : +213 XXX XXX XXX

### Garantie et Responsabilité

L'auteur décline toute responsabilité concernant :
- Les dommages directs ou indirects liés à l'utilisation
- La perte de données ou d'informations
- Les interruptions d'activité
- Les erreurs ou dysfonctionnements

## 🙏 Crédits et Remerciements

### 👨‍💻 Équipe de Développement

**Développeur Principal**
- Conception et architecture de l'application
- Implémentation des modules principaux
- Documentation et tests

### 📚 Technologies Utilisées

#### Frameworks et Bibliothèques Principales

**PyQt5**
- Licence : GPL v3 / Commercial
- Site : https://www.riverbankcomputing.com/software/pyqt/
- Usage : Interface utilisateur graphique

**SQLAlchemy**
- Licence : MIT
- Site : https://www.sqlalchemy.org/
- Usage : ORM et gestion de base de données

**bcrypt**
- Licence : Apache 2.0
- Site : https://github.com/pyca/bcrypt/
- Usage : Hashage sécurisé des mots de passe

#### Bibliothèques Optionnelles

**ReportLab**
- Licence : BSD
- Site : https://www.reportlab.com/
- Usage : Génération de documents PDF

**matplotlib**
- Licence : PSF (Python Software Foundation)
- Site : https://matplotlib.org/
- Usage : Génération de graphiques

**pandas**
- Licence : BSD
- Site : https://pandas.pydata.org/
- Usage : Manipulation et analyse de données

**openpyxl**
- Licence : MIT
- Site : https://openpyxl.readthedocs.io/
- Usage : Export Excel

**python-barcode**
- Licence : MIT
- Site : https://github.com/WhyNotHugo/python-barcode
- Usage : Génération de codes-barres

**pyzbar**
- Licence : MIT
- Site : https://github.com/NaturalHistoryMuseum/pyzbar
- Usage : Lecture de codes-barres

**OpenCV**
- Licence : Apache 2.0
- Site : https://opencv.org/
- Usage : Traitement d'images et caméra

**Pillow**
- Licence : PIL Software License
- Site : https://python-pillow.org/
- Usage : Manipulation d'images

**cryptography**
- Licence : Apache 2.0 / BSD
- Site : https://cryptography.io/
- Usage : Cryptographie et sécurité

### 🎨 Design et Interface

**Inspiration Design**
- Fluent Design System (Microsoft)
- Material Design (Google)
- Glassmorphism UI Trend

**Icônes et Ressources**
- Emojis Unicode pour les icônes temporaires
- Palette de couleurs moderne (bleu cyan, magenta)
- Typographie : Segoe UI, Orbitron

### 📖 Documentation et Guides

**Méthodologies**
- Keep a Changelog pour le versioning
- Semantic Versioning pour les numéros de version
- Conventional Commits pour les messages de commit

**Inspiration Documentation**
- Documentation Python officielle
- Guides de bonnes pratiques open source
- Standards de documentation technique

### 🏗️ Architecture et Patterns

**Patterns Architecturaux**
- Layered Architecture (Architecture en couches)
- Repository Pattern (Couche d'accès aux données)
- Service Layer Pattern (Logique métier)
- MVC Pattern (Modèle-Vue-Contrôleur)

**Bonnes Pratiques**
- SOLID Principles
- DRY (Don't Repeat Yourself)
- KISS (Keep It Simple, Stupid)
- Clean Code principles

### 🌍 Communauté et Support

**Ressources Communautaires**
- Stack Overflow pour les solutions techniques
- GitHub pour l'hébergement et la collaboration
- Python Package Index (PyPI) pour les dépendances
- Qt Documentation pour l'interface utilisateur

**Forums et Discussions**
- Reddit r/Python
- Python Discord
- Qt Forum
- SQLAlchemy Google Group

## 🔒 Sécurité et Confidentialité

### Protection des Données

L'application GSCOM respecte les principes suivants :

- **Stockage Local** : Toutes les données sont stockées localement
- **Chiffrement** : Mots de passe hashés avec bcrypt
- **Journalisation** : Logs sécurisés sans données sensibles
- **Accès** : Contrôle d'accès basé sur les rôles

### Conformité

- **RGPD** : Respect des principes de protection des données
- **Sécurité** : Bonnes pratiques de sécurité informatique
- **Audit** : Traçabilité des actions utilisateur

## 📞 Contact et Support

### Support Technique

**Email** : <EMAIL>
**Documentation** : Consultez les fichiers README.md et GUIDE_UTILISATEUR.md
**Issues** : Rapportez les bugs via le système de tickets

### Développement et Contributions

**Email** : <EMAIL>
**Suggestions** : Propositions d'amélioration bienvenues
**Partenariats** : Opportunités de collaboration

### Commercial

**Ventes** : <EMAIL>
**Licences** : <EMAIL>
**Partenaires** : <EMAIL>

## 📈 Évolution et Roadmap

### Versions Futures

Le développement de GSCOM continue avec :
- Nouvelles fonctionnalités
- Améliorations de performance
- Corrections de bugs
- Support de nouvelles plateformes

### Contribution

Les contributions sont les bienvenues sous forme de :
- Rapports de bugs
- Suggestions d'amélioration
- Documentation
- Tests et validation

---

## 🎯 Déclaration de Mission

**GSCOM** a été conçu pour démocratiser l'accès aux outils de gestion d'entreprise modernes. Notre mission est de fournir une solution complète, intuitive et accessible qui permet aux entreprises de toutes tailles de gérer efficacement leurs opérations commerciales, leurs stocks et leur comptabilité.

### Valeurs

- **Excellence** : Qualité du code et de l'expérience utilisateur
- **Innovation** : Technologies modernes et interface attractive
- **Accessibilité** : Solution abordable et facile à utiliser
- **Fiabilité** : Stabilité et sécurité des données
- **Évolutivité** : Architecture extensible et modulaire

---

**GSCOM v1.0.0** - *Merci à tous ceux qui ont contribué à faire de ce projet une réalité* 🙏

*Pour toute question concernant les licences ou les crédits, n'hésitez pas à nous contacter.*
