#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Module tableau de bord avec statistiques réelles
"""

import logging
from PyQt5.QtWidgets import *
from PyQt5.QtCore import *
from PyQt5.QtGui import *

from src.dal.database import db_manager
from src.dal.models.client import Client
from src.dal.models.product import Product
from src.dal.models.user import User

class DashboardWidget(QWidget):
    """Widget du tableau de bord avec statistiques réelles"""

    def __init__(self, current_user, parent=None):
        super().__init__(parent)
        self.current_user = current_user
        self.logger = logging.getLogger(__name__)
        self.setup_ui()
        self.load_statistics()

        # Timer pour actualiser les statistiques
        self.refresh_timer = QTimer()
        self.refresh_timer.timeout.connect(self.load_statistics)
        self.refresh_timer.start(30000)  # Actualiser toutes les 30 secondes

    def setup_ui(self):
        """Configure l'interface"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(20)

        # En-tête de bienvenue
        self.create_welcome_header(layout)

        # Cartes de statistiques
        self.create_stats_cards(layout)

        # Zone de contenu principal
        self.create_main_content(layout)

        # Actions rapides
        self.create_quick_actions(layout)

    def create_welcome_header(self, layout):
        """Crée l'en-tête de bienvenue"""
        header_frame = QFrame()
        header_frame.setObjectName("welcomeHeader")
        header_layout = QHBoxLayout(header_frame)
        header_layout.setContentsMargins(20, 15, 20, 15)

        # Message de bienvenue
        welcome_layout = QVBoxLayout()

        welcome_label = QLabel(f"Bienvenue, {self.current_user.first_name} !")
        welcome_label.setObjectName("welcomeLabel")
        welcome_layout.addWidget(welcome_label)

        from datetime import datetime
        date_label = QLabel(datetime.now().strftime("Nous sommes le %A %d %B %Y"))
        date_label.setObjectName("dateLabel")
        welcome_layout.addWidget(date_label)

        header_layout.addLayout(welcome_layout)
        header_layout.addStretch()

        # Bouton d'actualisation
        refresh_button = QPushButton("🔄 Actualiser")
        refresh_button.setObjectName("refreshButton")
        refresh_button.clicked.connect(self.load_statistics)
        header_layout.addWidget(refresh_button)

        layout.addWidget(header_frame)

    def create_stats_cards(self, layout):
        """Crée les cartes de statistiques"""
        stats_frame = QFrame()
        stats_frame.setObjectName("statsFrame")
        stats_layout = QHBoxLayout(stats_frame)
        stats_layout.setContentsMargins(0, 0, 0, 0)
        stats_layout.setSpacing(15)

        # Carte Clients
        self.clients_card = self.create_stat_card(
            "👥", "Clients", "0", "0 actifs", "#00d4ff"
        )
        stats_layout.addWidget(self.clients_card)

        # Carte Produits
        self.products_card = self.create_stat_card(
            "📦", "Produits", "0", "0 en stock", "#ff6b6b"
        )
        stats_layout.addWidget(self.products_card)

        # Carte Utilisateurs
        self.users_card = self.create_stat_card(
            "👤", "Utilisateurs", "0", "0 actifs", "#4ecdc4"
        )
        stats_layout.addWidget(self.users_card)

        # Carte Système
        self.system_card = self.create_stat_card(
            "⚙️", "Système", "OK", "Opérationnel", "#45b7d1"
        )
        stats_layout.addWidget(self.system_card)

        layout.addWidget(stats_frame)

    def create_stat_card(self, icon, title, value, subtitle, color):
        """Crée une carte de statistique"""
        card = QFrame()
        card.setObjectName("statCard")
        card.setFixedHeight(120)

        card_layout = QVBoxLayout(card)
        card_layout.setContentsMargins(20, 15, 20, 15)
        card_layout.setSpacing(5)

        # En-tête avec icône et titre
        header_layout = QHBoxLayout()

        icon_label = QLabel(icon)
        icon_label.setObjectName("statIcon")
        icon_label.setStyleSheet(f"font-size: 24px; color: {color};")
        header_layout.addWidget(icon_label)

        title_label = QLabel(title)
        title_label.setObjectName("statTitle")
        header_layout.addWidget(title_label)

        header_layout.addStretch()
        card_layout.addLayout(header_layout)

        # Valeur principale
        value_label = QLabel(value)
        value_label.setObjectName("statValue")
        card_layout.addWidget(value_label)

        # Sous-titre
        subtitle_label = QLabel(subtitle)
        subtitle_label.setObjectName("statSubtitle")
        card_layout.addWidget(subtitle_label)

        card_layout.addStretch()

        return card

    def create_main_content(self, layout):
        """Crée la zone de contenu principal"""
        content_frame = QFrame()
        content_frame.setObjectName("mainContent")
        content_layout = QHBoxLayout(content_frame)
        content_layout.setContentsMargins(0, 0, 0, 0)
        content_layout.setSpacing(15)

        # Colonne gauche - Informations système
        left_column = self.create_system_info()
        content_layout.addWidget(left_column, 1)

        # Colonne droite - Activité récente
        right_column = self.create_recent_activity()
        content_layout.addWidget(right_column, 1)

        layout.addWidget(content_frame)

    def create_system_info(self):
        """Crée la section d'informations système"""
        frame = QFrame()
        frame.setObjectName("infoSection")
        layout = QVBoxLayout(frame)
        layout.setContentsMargins(20, 15, 20, 15)

        # Titre
        title = QLabel("📊 Informations Système")
        title.setObjectName("sectionTitle")
        layout.addWidget(title)

        # Contenu
        info_text = QLabel("""
        <b>🎉 Application GSCOM v1.0.0</b><br><br>

        <b>✅ Fonctionnalités disponibles :</b><br>
        • Gestion des clients et fournisseurs<br>
        • Catalogue produits avec catégories<br>
        • Système d'authentification sécurisé<br>
        • Interface moderne et intuitive<br>
        • Base de données SQLite intégrée<br><br>

        <b>🚀 Modules opérationnels :</b><br>
        • Tableau de bord avec statistiques<br>
        • Gestion des clients<br>
        • Gestion des produits<br>
        • Système de sécurité<br><br>

        <b>🔧 En développement :</b><br>
        • Module commercial complet<br>
        • Gestion des stocks<br>
        • Comptabilité intégrée<br>
        • Rapports et analyses
        """)
        info_text.setObjectName("infoText")
        info_text.setWordWrap(True)
        layout.addWidget(info_text)

        layout.addStretch()

        return frame

    def create_recent_activity(self):
        """Crée la section d'activité récente"""
        frame = QFrame()
        frame.setObjectName("activitySection")
        layout = QVBoxLayout(frame)
        layout.setContentsMargins(20, 15, 20, 15)

        # Titre
        title = QLabel("📈 Activité Récente")
        title.setObjectName("sectionTitle")
        layout.addWidget(title)

        # Liste d'activité
        self.activity_list = QListWidget()
        self.activity_list.setObjectName("activityList")
        layout.addWidget(self.activity_list)

        return frame

    def create_quick_actions(self, layout):
        """Crée la section d'actions rapides"""
        actions_frame = QFrame()
        actions_frame.setObjectName("actionsFrame")
        actions_layout = QVBoxLayout(actions_frame)
        actions_layout.setContentsMargins(20, 15, 20, 15)

        # Titre
        title = QLabel("⚡ Actions Rapides")
        title.setObjectName("sectionTitle")
        actions_layout.addWidget(title)

        # Boutons d'action
        buttons_layout = QHBoxLayout()

        new_client_btn = QPushButton("👥 Nouveau Client")
        new_client_btn.setObjectName("actionButton")
        new_client_btn.clicked.connect(self.new_client)
        buttons_layout.addWidget(new_client_btn)

        new_product_btn = QPushButton("📦 Nouveau Produit")
        new_product_btn.setObjectName("actionButton")
        new_product_btn.clicked.connect(self.new_product)
        buttons_layout.addWidget(new_product_btn)

        view_clients_btn = QPushButton("👁️ Voir Clients")
        view_clients_btn.setObjectName("actionButton")
        view_clients_btn.clicked.connect(self.view_clients)
        buttons_layout.addWidget(view_clients_btn)

        view_products_btn = QPushButton("👁️ Voir Produits")
        view_products_btn.setObjectName("actionButton")
        view_products_btn.clicked.connect(self.view_products)
        buttons_layout.addWidget(view_products_btn)

        actions_layout.addLayout(buttons_layout)
        layout.addWidget(actions_frame)

    def load_statistics(self):
        """Charge les statistiques réelles"""
        try:
            with db_manager.get_session() as session:
                # Statistiques clients
                total_clients = session.query(Client).count()
                active_clients = session.query(Client).filter(Client.is_active == True).count()

                # Statistiques produits
                total_products = session.query(Product).count()
                products_in_stock = session.query(Product).filter(
                    Product.track_stock == True,
                    Product.current_stock > 0
                ).count()

                # Statistiques utilisateurs
                total_users = session.query(User).count()
                active_users = session.query(User).filter(User.is_active == True).count()

                # Mettre à jour les cartes
                self.update_stat_card(self.clients_card, str(total_clients), f"{active_clients} actifs")
                self.update_stat_card(self.products_card, str(total_products), f"{products_in_stock} en stock")
                self.update_stat_card(self.users_card, str(total_users), f"{active_users} actifs")

                # Mettre à jour l'activité
                self.update_activity()

                self.logger.info("Statistiques mises à jour")

        except Exception as e:
            self.logger.error(f"Erreur lors du chargement des statistiques: {e}")

    def update_stat_card(self, card, value, subtitle):
        """Met à jour une carte de statistique"""
        # Trouver les labels dans la carte
        for child in card.findChildren(QLabel):
            if child.objectName() == "statValue":
                child.setText(value)
            elif child.objectName() == "statSubtitle":
                child.setText(subtitle)

    def update_activity(self):
        """Met à jour la liste d'activité"""
        self.activity_list.clear()

        from datetime import datetime
        current_time = datetime.now().strftime("%H:%M")

        activities = [
            f"🔄 {current_time} - Statistiques actualisées",
            f"👤 {current_time} - Connexion de {self.current_user.username}",
            f"📊 {current_time} - Tableau de bord affiché",
            f"✅ {current_time} - Système opérationnel",
        ]

        for activity in activities:
            item = QListWidgetItem(activity)
            self.activity_list.addItem(item)

    def new_client(self):
        """Ouvre le formulaire de nouveau client"""
        main_window = self.get_main_window()
        if main_window:
            main_window.switch_module("clients")

    def new_product(self):
        """Ouvre le formulaire de nouveau produit"""
        main_window = self.get_main_window()
        if main_window:
            main_window.switch_module("products")

    def view_clients(self):
        """Affiche la liste des clients"""
        main_window = self.get_main_window()
        if main_window:
            main_window.switch_module("clients")

    def view_products(self):
        """Affiche la liste des produits"""
        main_window = self.get_main_window()
        if main_window:
            main_window.switch_module("products")

    def get_main_window(self):
        """Récupère la fenêtre principale"""
        widget = self
        while widget:
            if hasattr(widget, 'switch_module'):
                return widget
            widget = widget.parent()
        return None

    def trigger_new_action(self, module):
        """Déclenche l'action nouveau dans un module"""
        # Cette méthode sera appelée après le changement de module
        pass

    def apply_styles(self):
        """Applique les styles CSS"""
        self.setStyleSheet("""
            #welcomeHeader {
                background: rgba(255, 255, 255, 0.05);
                border-radius: 12px;
                border: 1px solid rgba(255, 255, 255, 0.1);
            }

            #welcomeLabel {
                font-size: 28px;
                font-weight: bold;
                color: #00d4ff;
                margin-bottom: 5px;
            }

            #dateLabel {
                font-size: 14px;
                color: rgba(255, 255, 255, 0.8);
            }

            #refreshButton {
                background: rgba(0, 212, 255, 0.2);
                border: 1px solid rgba(0, 212, 255, 0.5);
                border-radius: 6px;
                padding: 8px 16px;
                color: #00d4ff;
                font-weight: 500;
            }

            #refreshButton:hover {
                background: rgba(0, 212, 255, 0.3);
            }

            #statCard {
                background: rgba(255, 255, 255, 0.05);
                border: 1px solid rgba(255, 255, 255, 0.1);
                border-radius: 12px;
            }

            #statCard:hover {
                background: rgba(255, 255, 255, 0.08);
                border-color: rgba(0, 212, 255, 0.3);
            }

            #statTitle {
                font-size: 12px;
                color: rgba(255, 255, 255, 0.8);
                font-weight: 500;
            }

            #statValue {
                font-size: 24px;
                font-weight: bold;
                color: #ffffff;
                margin: 5px 0;
            }

            #statSubtitle {
                font-size: 11px;
                color: rgba(255, 255, 255, 0.6);
            }

            #infoSection, #activitySection, #actionsFrame {
                background: rgba(255, 255, 255, 0.03);
                border: 1px solid rgba(255, 255, 255, 0.1);
                border-radius: 12px;
            }

            #sectionTitle {
                font-size: 16px;
                font-weight: bold;
                color: #00d4ff;
                margin-bottom: 15px;
            }

            #infoText {
                color: rgba(255, 255, 255, 0.9);
                line-height: 1.4;
            }

            #activityList {
                background: transparent;
                border: none;
                color: rgba(255, 255, 255, 0.9);
            }

            #activityList::item {
                padding: 8px;
                border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            }

            #activityList::item:hover {
                background: rgba(0, 212, 255, 0.1);
            }

            #actionButton {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(0, 212, 255, 0.2),
                    stop:1 rgba(255, 0, 255, 0.2));
                border: 1px solid rgba(0, 212, 255, 0.5);
                border-radius: 8px;
                padding: 12px 20px;
                color: white;
                font-weight: 500;
                font-size: 14px;
            }

            #actionButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(0, 212, 255, 0.3),
                    stop:1 rgba(255, 0, 255, 0.3));
                border-color: #00d4ff;
            }
        """)

    def showEvent(self, event):
        """Événement d'affichage"""
        super().showEvent(event)
        self.apply_styles()
        self.load_statistics()
