#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Service de base pour la couche logique métier
"""

import logging
from typing import List, Optional, Dict, Any, Type, TypeVar
from sqlalchemy.orm import Session
from sqlalchemy.exc import SQLAlchemyError

from src.dal.database import db_manager, Base

T = TypeVar('T', bound=Base)

class BaseService:
    """Service de base pour toutes les opérations métier"""

    def __init__(self, model_class: Type[T]):
        self.model_class = model_class
        self.logger = logging.getLogger(self.__class__.__name__)

    def get_all(self, session: Optional[Session] = None, **filters) -> List[T]:
        """Récupère tous les enregistrements avec filtres optionnels"""
        try:
            if session:
                query = session.query(self.model_class)
            else:
                with db_manager.get_session() as session:
                    query = session.query(self.model_class)

                    # Appliquer les filtres
                    for key, value in filters.items():
                        if hasattr(self.model_class, key):
                            query = query.filter(getattr(self.model_class, key) == value)

                    return query.all()

            # Si session fournie, ne pas utiliser le context manager
            for key, value in filters.items():
                if hasattr(self.model_class, key):
                    query = query.filter(getattr(self.model_class, key) == value)

            return query.all()

        except SQLAlchemyError as e:
            self.logger.error(f"Erreur lors de la récupération des {self.model_class.__name__}: {e}")
            return []

    def get_by_id(self, id: int, session: Optional[Session] = None) -> Optional[T]:
        """Récupère un enregistrement par son ID"""
        try:
            if session:
                return session.query(self.model_class).filter(self.model_class.id == id).first()
            else:
                with db_manager.get_session() as session:
                    return session.query(self.model_class).filter(self.model_class.id == id).first()

        except SQLAlchemyError as e:
            self.logger.error(f"Erreur lors de la récupération de {self.model_class.__name__} ID {id}: {e}")
            return None

    def get_by_field(self, field_name: str, value: Any, session: Optional[Session] = None) -> Optional[T]:
        """Récupère un enregistrement par un champ spécifique"""
        try:
            if not hasattr(self.model_class, field_name):
                raise ValueError(f"Le champ {field_name} n'existe pas dans {self.model_class.__name__}")

            if session:
                return session.query(self.model_class).filter(getattr(self.model_class, field_name) == value).first()
            else:
                with db_manager.get_session() as session:
                    return session.query(self.model_class).filter(getattr(self.model_class, field_name) == value).first()

        except SQLAlchemyError as e:
            self.logger.error(f"Erreur lors de la récupération de {self.model_class.__name__} par {field_name}={value}: {e}")
            return None

    def create(self, data: Dict[str, Any], session: Optional[Session] = None) -> Optional[T]:
        """Crée un nouvel enregistrement"""
        try:
            # Valider les données avant création
            validation_errors = self.validate_data(data, is_update=False)
            if validation_errors:
                self.logger.warning(f"Erreurs de validation lors de la création: {validation_errors}")
                return None

            # Créer l'instance
            instance = self.model_class(**data)

            if session:
                session.add(instance)
                session.flush()  # Pour obtenir l'ID
                return instance
            else:
                with db_manager.get_session() as db_session:
                    db_session.add(instance)
                    db_session.flush()
                    # Créer une nouvelle instance détachée avec les mêmes données
                    result_data = {c.name: getattr(instance, c.name) for c in instance.__table__.columns}
                    return self.model_class(**result_data)

        except SQLAlchemyError as e:
            self.logger.error(f"Erreur lors de la création de {self.model_class.__name__}: {e}")
            return None

    def update(self, id: int, data: Dict[str, Any], session: Optional[Session] = None) -> Optional[T]:
        """Met à jour un enregistrement"""
        try:
            # Valider les données
            validation_errors = self.validate_data(data, is_update=True)
            if validation_errors:
                self.logger.warning(f"Erreurs de validation lors de la mise à jour: {validation_errors}")
                return None

            if session:
                instance = session.query(self.model_class).filter(self.model_class.id == id).first()
                if instance:
                    for key, value in data.items():
                        if hasattr(instance, key):
                            setattr(instance, key, value)
                    session.flush()
                    return instance
                return None
            else:
                with db_manager.get_session() as session:
                    instance = session.query(self.model_class).filter(self.model_class.id == id).first()
                    if instance:
                        for key, value in data.items():
                            if hasattr(instance, key):
                                setattr(instance, key, value)
                        session.flush()
                        session.refresh(instance)
                        return instance
                    return None

        except SQLAlchemyError as e:
            self.logger.error(f"Erreur lors de la mise à jour de {self.model_class.__name__} ID {id}: {e}")
            return None

    def delete(self, id: int, session: Optional[Session] = None) -> bool:
        """Supprime un enregistrement"""
        try:
            if session:
                instance = session.query(self.model_class).filter(self.model_class.id == id).first()
                if instance:
                    session.delete(instance)
                    session.flush()
                    return True
                return False
            else:
                with db_manager.get_session() as session:
                    instance = session.query(self.model_class).filter(self.model_class.id == id).first()
                    if instance:
                        session.delete(instance)
                        return True
                    return False

        except SQLAlchemyError as e:
            self.logger.error(f"Erreur lors de la suppression de {self.model_class.__name__} ID {id}: {e}")
            return False

    def count(self, session: Optional[Session] = None, **filters) -> int:
        """Compte le nombre d'enregistrements avec filtres optionnels"""
        try:
            if session:
                query = session.query(self.model_class)
            else:
                with db_manager.get_session() as session:
                    query = session.query(self.model_class)

                    # Appliquer les filtres
                    for key, value in filters.items():
                        if hasattr(self.model_class, key):
                            query = query.filter(getattr(self.model_class, key) == value)

                    return query.count()

            # Si session fournie
            for key, value in filters.items():
                if hasattr(self.model_class, key):
                    query = query.filter(getattr(self.model_class, key) == value)

            return query.count()

        except SQLAlchemyError as e:
            self.logger.error(f"Erreur lors du comptage des {self.model_class.__name__}: {e}")
            return 0

    def exists(self, id: int, session: Optional[Session] = None) -> bool:
        """Vérifie si un enregistrement existe"""
        try:
            if session:
                return session.query(self.model_class).filter(self.model_class.id == id).first() is not None
            else:
                with db_manager.get_session() as session:
                    return session.query(self.model_class).filter(self.model_class.id == id).first() is not None

        except SQLAlchemyError as e:
            self.logger.error(f"Erreur lors de la vérification d'existence de {self.model_class.__name__} ID {id}: {e}")
            return False

    def validate_data(self, data: Dict[str, Any], is_update: bool = False) -> List[str]:
        """Valide les données (à surcharger dans les services spécialisés)"""
        errors = []

        # Validation de base - vérifier que les champs existent
        for key in data.keys():
            if not hasattr(self.model_class, key):
                errors.append(f"Le champ '{key}' n'existe pas dans {self.model_class.__name__}")

        return errors

    def search(self, search_term: str, fields: List[str], session: Optional[Session] = None) -> List[T]:
        """Recherche dans les champs spécifiés"""
        try:
            if session:
                query = session.query(self.model_class)
            else:
                with db_manager.get_session() as session:
                    query = session.query(self.model_class)

                    # Construire la condition de recherche
                    conditions = []
                    for field in fields:
                        if hasattr(self.model_class, field):
                            field_attr = getattr(self.model_class, field)
                            conditions.append(field_attr.ilike(f"%{search_term}%"))

                    if conditions:
                        from sqlalchemy import or_
                        query = query.filter(or_(*conditions))

                    return query.all()

            # Si session fournie
            conditions = []
            for field in fields:
                if hasattr(self.model_class, field):
                    field_attr = getattr(self.model_class, field)
                    conditions.append(field_attr.ilike(f"%{search_term}%"))

            if conditions:
                from sqlalchemy import or_
                query = query.filter(or_(*conditions))

            return query.all()

        except SQLAlchemyError as e:
            self.logger.error(f"Erreur lors de la recherche dans {self.model_class.__name__}: {e}")
            return []
