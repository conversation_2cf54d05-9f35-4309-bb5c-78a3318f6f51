#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Script pour créer des mouvements de stock d'exemple
"""

import sys
import os
from datetime import datetime, timedelta

# Ajouter le répertoire src au path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from src.dal.database import db_manager
from src.dal.models.stock import StockMovement, MovementType, MovementReason
from src.dal.models.product import Product
from src.dal.models.user import User

def create_sample_movements():
    """Crée des mouvements de stock d'exemple"""
    print("📦 Création de mouvements de stock d'exemple...")
    
    try:
        with db_manager.get_session() as session:
            # Récupérer les produits et utilisateurs
            products = session.query(Product).filter(Product.track_stock == True).all()
            admin_user = session.query(User).filter(User.username == 'admin').first()
            
            if not products:
                print("❌ Aucun produit avec suivi de stock trouvé")
                return
            
            if not admin_user:
                print("❌ Utilisateur admin introuvable")
                return
            
            # Mouvements d'exemple
            movements_data = []
            
            # Entrées de stock (achats)
            for i, product in enumerate(products[:3]):  # Premiers 3 produits
                movements_data.append({
                    'product_id': product.id,
                    'movement_type': MovementType.IN,
                    'quantity': 50 + (i * 10),
                    'reason': MovementReason.PURCHASE,
                    'reference': f"ACH{2024001 + i}",
                    'notes': f"Achat initial de {product.name}",
                    'date': datetime.now() - timedelta(days=30 - i),
                    'user_id': admin_user.id
                })
            
            # Ventes (sorties)
            for i, product in enumerate(products[:4]):  # Premiers 4 produits
                movements_data.append({
                    'product_id': product.id,
                    'movement_type': MovementType.OUT,
                    'quantity': -(5 + (i * 2)),
                    'reason': MovementReason.SALE,
                    'reference': f"VTE{2024001 + i}",
                    'notes': f"Vente de {product.name}",
                    'date': datetime.now() - timedelta(days=15 - i),
                    'user_id': admin_user.id
                })
            
            # Retours clients
            if len(products) > 0:
                movements_data.append({
                    'product_id': products[0].id,
                    'movement_type': MovementType.IN,
                    'quantity': 2,
                    'reason': MovementReason.RETURN_CUSTOMER,
                    'reference': "RET2024001",
                    'notes': f"Retour client - {products[0].name}",
                    'date': datetime.now() - timedelta(days=10),
                    'user_id': admin_user.id
                })
            
            # Ajustements d'inventaire
            for i, product in enumerate(products[1:3]):  # Produits 2 et 3
                movements_data.append({
                    'product_id': product.id,
                    'movement_type': MovementType.ADJUSTMENT,
                    'quantity': -1 if i == 0 else 3,
                    'reason': MovementReason.INVENTORY,
                    'reference': f"INV2024{i+1:03d}",
                    'notes': f"Ajustement inventaire - {product.name}",
                    'date': datetime.now() - timedelta(days=5),
                    'user_id': admin_user.id
                })
            
            # Pertes
            if len(products) > 2:
                movements_data.append({
                    'product_id': products[2].id,
                    'movement_type': MovementType.OUT,
                    'quantity': -1,
                    'reason': MovementReason.LOSS,
                    'reference': "PRT2024001",
                    'notes': f"Perte - {products[2].name}",
                    'date': datetime.now() - timedelta(days=3),
                    'user_id': admin_user.id
                })
            
            # Mouvements récents
            for i, product in enumerate(products[:2]):
                movements_data.append({
                    'product_id': product.id,
                    'movement_type': MovementType.OUT,
                    'quantity': -(2 + i),
                    'reason': MovementReason.SALE,
                    'reference': f"VTE{2024010 + i}",
                    'notes': f"Vente récente - {product.name}",
                    'date': datetime.now() - timedelta(hours=24 - (i * 6)),
                    'user_id': admin_user.id
                })
            
            # Créer les mouvements et mettre à jour les stocks
            created_count = 0
            for movement_data in movements_data:
                # Vérifier si le mouvement existe déjà
                existing = session.query(StockMovement).filter(
                    StockMovement.product_id == movement_data['product_id'],
                    StockMovement.reference == movement_data['reference']
                ).first()
                
                if not existing:
                    # Créer le mouvement
                    movement = StockMovement(**movement_data)
                    session.add(movement)
                    
                    # Mettre à jour le stock du produit
                    product = session.query(Product).filter(Product.id == movement_data['product_id']).first()
                    if product:
                        product.current_stock += movement_data['quantity']
                        if product.current_stock < 0:
                            product.current_stock = 0
                    
                    created_count += 1
            
            session.commit()
            print(f"✅ {created_count} mouvements de stock créés")
            
            # Afficher les stocks mis à jour
            print("\n📊 Stocks mis à jour :")
            for product in products:
                session.refresh(product)
                status = ""
                if product.is_out_of_stock:
                    status = " ❌ RUPTURE"
                elif product.is_low_stock:
                    status = " ⚠️ STOCK BAS"
                else:
                    status = " ✅ OK"
                
                print(f"   {product.name}: {product.current_stock:.0f}{status}")
            
    except Exception as e:
        print(f"❌ Erreur lors de la création des mouvements: {e}")
        import traceback
        traceback.print_exc()

def show_stock_statistics():
    """Affiche les statistiques de stock"""
    print("\n📈 Statistiques de stock :")
    
    try:
        with db_manager.get_session() as session:
            # Compter les mouvements
            total_movements = session.query(StockMovement).count()
            entries = session.query(StockMovement).filter(StockMovement.movement_type == MovementType.IN).count()
            exits = session.query(StockMovement).filter(StockMovement.movement_type == MovementType.OUT).count()
            adjustments = session.query(StockMovement).filter(StockMovement.movement_type == MovementType.ADJUSTMENT).count()
            
            print(f"   📦 Total mouvements: {total_movements}")
            print(f"   📥 Entrées: {entries}")
            print(f"   📤 Sorties: {exits}")
            print(f"   ⚖️ Ajustements: {adjustments}")
            
            # Statistiques produits
            products_with_stock = session.query(Product).filter(
                Product.track_stock == True,
                Product.current_stock > 0
            ).count()
            
            low_stock = session.query(Product).filter(
                Product.track_stock == True,
                Product.current_stock <= Product.min_stock,
                Product.current_stock > 0
            ).count()
            
            out_of_stock = session.query(Product).filter(
                Product.track_stock == True,
                Product.current_stock <= 0
            ).count()
            
            print(f"\n   📊 Produits en stock: {products_with_stock}")
            print(f"   ⚠️ Stock bas: {low_stock}")
            print(f"   ❌ Ruptures: {out_of_stock}")
            
            # Valeur du stock
            products = session.query(Product).filter(
                Product.track_stock == True,
                Product.current_stock > 0
            ).all()
            
            total_value = 0
            for product in products:
                if product.purchase_price and product.current_stock > 0:
                    total_value += float(product.purchase_price) * product.current_stock
            
            print(f"   💰 Valeur stock (prix achat): {total_value:,.2f} DA")
            
    except Exception as e:
        print(f"❌ Erreur lors du calcul des statistiques: {e}")

def main():
    """Fonction principale"""
    print("🚀 Création de mouvements de stock pour GSCOM...")
    print("=" * 50)
    
    try:
        create_sample_movements()
        show_stock_statistics()
        
        print("\n" + "=" * 50)
        print("🎉 Mouvements de stock créés avec succès !")
        print("\n💡 Nouvelles fonctionnalités disponibles :")
        print("   • Historique des mouvements de stock")
        print("   • Alertes de stock bas et ruptures")
        print("   • Suivi des entrées et sorties")
        print("   • Gestion des ajustements d'inventaire")
        
        print("\n🚀 Testez le module Stock dans l'application :")
        print("   python main.py")
        print("   → Navigation : Stock")
        
    except Exception as e:
        print(f"\n❌ Erreur: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
