// qgeolocation.sip generated by MetaSIP
//
// This file is part of the QtPositioning Python extension module.
//
// Copyright (c) 2024 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt5.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


%If (Qt_5_2_0 -)

class QGeoLocation
{
%TypeHeaderCode
#include <qgeolocation.h>
%End

public:
    QGeoLocation();
    QGeoLocation(const QGeoLocation &other);
    ~QGeoLocation();
    bool operator==(const QGeoLocation &other) const;
    bool operator!=(const QGeoLocation &other) const;
    QGeoAddress address() const;
    void setAddress(const QGeoAddress &address);
    QGeoCoordinate coordinate() const;
    void setCoordinate(const QGeoCoordinate &position);
    QGeoRectangle boundingBox() const;
    void setBoundingBox(const QGeoRectangle &box);
    bool isEmpty() const;
%If (Qt_5_13_0 -)
    QVariantMap extendedAttributes() const;
%End
%If (Qt_5_13_0 -)
    void setExtendedAttributes(const QVariantMap &data);
%End
};

%End
