#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Script pour créer des données d'exemple dans GSCOM
"""

import sys
import os

# Ajouter le répertoire src au path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from src.dal.database import db_manager
from src.dal.models.product import Category, Unit
from src.dal.models.client import Client, ClientType, PaymentTerms

def create_sample_categories():
    """Crée des catégories d'exemple"""
    categories_data = [
        {"code": "CAT001", "name": "Électronique", "description": "Produits électroniques et informatiques"},
        {"code": "CAT002", "name": "Mobilier", "description": "Mobilier de bureau et domestique"},
        {"code": "CAT003", "name": "Fournitures", "description": "Fournitures de bureau et consommables"},
        {"code": "CAT004", "name": "Vêtements", "description": "Vêtements et accessoires"},
        {"code": "CAT005", "name": "Alimentation", "description": "Produits alimentaires et boissons"},
    ]
    
    with db_manager.get_session() as session:
        for cat_data in categories_data:
            # Vérifier si la catégorie existe déjà
            existing = session.query(Category).filter(Category.code == cat_data["code"]).first()
            if not existing:
                category = Category(**cat_data)
                session.add(category)
        
        session.commit()
        print("✅ Catégories créées")

def create_sample_units():
    """Crée des unités d'exemple"""
    units_data = [
        {"code": "PC", "name": "Pièce", "symbol": "pc", "description": "Unité de comptage"},
        {"code": "KG", "name": "Kilogramme", "symbol": "kg", "description": "Unité de poids"},
        {"code": "L", "name": "Litre", "symbol": "l", "description": "Unité de volume"},
        {"code": "M", "name": "Mètre", "symbol": "m", "description": "Unité de longueur"},
        {"code": "M2", "name": "Mètre carré", "symbol": "m²", "description": "Unité de surface"},
        {"code": "BOX", "name": "Boîte", "symbol": "box", "description": "Conditionnement en boîte"},
        {"code": "PAQ", "name": "Paquet", "symbol": "paq", "description": "Conditionnement en paquet"},
    ]
    
    with db_manager.get_session() as session:
        for unit_data in units_data:
            # Vérifier si l'unité existe déjà
            existing = session.query(Unit).filter(Unit.code == unit_data["code"]).first()
            if not existing:
                unit = Unit(**unit_data)
                session.add(unit)
        
        session.commit()
        print("✅ Unités créées")

def create_sample_clients():
    """Crée des clients d'exemple"""
    clients_data = [
        {
            "code": "CLI001",
            "name": "Ahmed Benali",
            "client_type": ClientType.INDIVIDUAL,
            "email": "<EMAIL>",
            "phone": "+*********** 456",
            "city": "Alger",
            "is_active": True
        },
        {
            "code": "CLI002",
            "name": "Fatima Kaci",
            "company_name": "SARL TechnoPlus",
            "client_type": ClientType.COMPANY,
            "email": "<EMAIL>",
            "phone": "+*********** 012",
            "city": "Oran",
            "tax_id": "123456789012345",
            "payment_terms": PaymentTerms.NET_30,
            "credit_limit": 50000.00,
            "is_active": True,
            "is_vip": True
        },
        {
            "code": "CLI003",
            "name": "Mohamed Cherif",
            "client_type": ClientType.INDIVIDUAL,
            "email": "<EMAIL>",
            "phone": "+*********** 678",
            "city": "Constantine",
            "is_active": True
        },
        {
            "code": "CLI004",
            "name": "Directeur Général",
            "company_name": "Ministère de l'Éducation",
            "client_type": ClientType.GOVERNMENT,
            "email": "<EMAIL>",
            "phone": "+*********** 234",
            "city": "Alger",
            "payment_terms": PaymentTerms.NET_60,
            "is_active": True
        },
        {
            "code": "CLI005",
            "name": "Amina Boudiaf",
            "company_name": "Entreprise Boudiaf & Fils",
            "client_type": ClientType.COMPANY,
            "email": "<EMAIL>",
            "phone": "+*********** 890",
            "city": "Annaba",
            "tax_id": "987654321098765",
            "payment_terms": PaymentTerms.NET_30,
            "credit_limit": 25000.00,
            "is_active": True
        }
    ]
    
    with db_manager.get_session() as session:
        for client_data in clients_data:
            # Vérifier si le client existe déjà
            existing = session.query(Client).filter(Client.code == client_data["code"]).first()
            if not existing:
                client = Client(**client_data)
                session.add(client)
        
        session.commit()
        print("✅ Clients d'exemple créés")

def main():
    """Fonction principale"""
    print("🚀 Création des données d'exemple pour GSCOM...")
    
    try:
        create_sample_categories()
        create_sample_units()
        create_sample_clients()
        
        print("\n🎉 Données d'exemple créées avec succès !")
        print("\n📋 Résumé :")
        print("   - 5 catégories de produits")
        print("   - 7 unités de mesure")
        print("   - 5 clients d'exemple")
        print("\n💡 Vous pouvez maintenant tester les modules Clients et Produits")
        
    except Exception as e:
        print(f"❌ Erreur lors de la création des données: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
