// qnmeapositioninfosource.sip generated by MetaSIP
//
// This file is part of the QtPositioning Python extension module.
//
// Copyright (c) 2024 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt5.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


%If (Qt_5_2_0 -)

class QNmeaPositionInfoSource : public QGeoPositionInfoSource
{
%TypeHeaderCode
#include <qnmeapositioninfosource.h>
%End

public:
    enum UpdateMode
    {
        RealTimeMode,
        SimulationMode,
    };

    QNmeaPositionInfoSource(QNmeaPositionInfoSource::UpdateMode updateMode, QObject *parent /TransferThis/ = 0);
    virtual ~QNmeaPositionInfoSource();
    QNmeaPositionInfoSource::UpdateMode updateMode() const;
    void setDevice(QIODevice *source);
    QIODevice *device() const;
    virtual void setUpdateInterval(int msec);
    virtual QGeoPositionInfo lastKnownPosition(bool fromSatellitePositioningMethodsOnly = false) const;
    virtual QGeoPositionInfoSource::PositioningMethods supportedPositioningMethods() const;
    virtual int minimumUpdateInterval() const;
    virtual QGeoPositionInfoSource::Error error() const;

public slots:
    virtual void startUpdates();
    virtual void stopUpdates();
    virtual void requestUpdate(int timeout = 0);

protected:
    virtual bool parsePosInfoFromNmeaData(const char *data /Encoding="None"/, int size, QGeoPositionInfo *posInfo, bool *hasFix);

public:
%If (Qt_5_3_0 -)
    void setUserEquivalentRangeError(double uere);
%End
%If (Qt_5_3_0 -)
    double userEquivalentRangeError() const;
%End
};

%End
