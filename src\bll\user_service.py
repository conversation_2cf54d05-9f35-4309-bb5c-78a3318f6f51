#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Service pour la gestion des utilisateurs et de la sécurité
"""

import logging
from typing import List, Optional, Dict, Any
from datetime import datetime, timedelta
from sqlalchemy.orm import Session
from sqlalchemy.exc import SQLAlchemyError

from src.bll.base_service import BaseService
from src.dal.models.user import User, Role, Permission
from src.dal.database import db_manager

class UserService(BaseService):
    """Service pour la gestion des utilisateurs"""

    def __init__(self):
        super().__init__(User)
        self.logger = logging.getLogger(__name__)

    def authenticate(self, username: str, password: str) -> Optional[User]:
        """Authentifie un utilisateur"""
        try:
            with db_manager.get_session() as session:
                user = session.query(User).filter(
                    (User.username == username) | (User.email == username)
                ).first()

                if not user:
                    self.logger.warning(f"Tentative de connexion avec utilisateur inexistant: {username}")
                    return None

                if not user.is_active:
                    self.logger.warning(f"Tentative de connexion avec compte inactif: {username}")
                    return None

                if user.is_locked():
                    self.logger.warning(f"Tentative de connexion avec compte verrouillé: {username}")
                    return None

                if user.check_password(password):
                    user.record_login_attempt(True)
                    session.commit()
                    # Rafraîchir l'objet pour éviter les problèmes de session
                    session.refresh(user)
                    self.logger.info(f"Connexion réussie pour: {user.username}")

                    # Créer un nouvel objet détaché avec les données nécessaires
                    user_data = {
                        'id': user.id,
                        'username': user.username,
                        'email': user.email,
                        'first_name': user.first_name,
                        'last_name': user.last_name,
                        'is_admin': user.is_admin,
                        'is_active': user.is_active
                    }

                    # Créer un nouvel objet User avec ces données
                    detached_user = User(**user_data)
                    detached_user.id = user.id
                    return detached_user
                else:
                    user.record_login_attempt(False)
                    session.commit()
                    self.logger.warning(f"Mot de passe incorrect pour: {username}")
                    return None

        except SQLAlchemyError as e:
            self.logger.error(f"Erreur lors de l'authentification: {e}")
            return None

    def create_user(self, data: Dict[str, Any]) -> Optional[User]:
        """Crée un nouvel utilisateur"""
        try:
            # Vérifier l'unicité du nom d'utilisateur et email
            with db_manager.get_session() as session:
                existing_user = session.query(User).filter(
                    (User.username == data.get('username')) |
                    (User.email == data.get('email'))
                ).first()

                if existing_user:
                    self.logger.warning(f"Utilisateur ou email déjà existant: {data.get('username')}, {data.get('email')}")
                    return None

                # Créer l'utilisateur
                password = data.pop('password', None)
                user = User(**data)

                if password:
                    user.set_password(password)

                session.add(user)
                session.commit()
                session.refresh(user)

                self.logger.info(f"Utilisateur créé: {user.username}")
                return user

        except SQLAlchemyError as e:
            self.logger.error(f"Erreur lors de la création de l'utilisateur: {e}")
            return None

    def change_password(self, user_id: int, old_password: str, new_password: str) -> bool:
        """Change le mot de passe d'un utilisateur"""
        try:
            with db_manager.get_session() as session:
                user = session.query(User).filter(User.id == user_id).first()

                if not user:
                    return False

                if not user.check_password(old_password):
                    self.logger.warning(f"Ancien mot de passe incorrect pour l'utilisateur {user.username}")
                    return False

                user.set_password(new_password)
                session.commit()

                self.logger.info(f"Mot de passe changé pour l'utilisateur {user.username}")
                return True

        except SQLAlchemyError as e:
            self.logger.error(f"Erreur lors du changement de mot de passe: {e}")
            return False

    def reset_password(self, user_id: int, new_password: str) -> bool:
        """Réinitialise le mot de passe d'un utilisateur (admin)"""
        try:
            with db_manager.get_session() as session:
                user = session.query(User).filter(User.id == user_id).first()

                if not user:
                    return False

                user.set_password(new_password)
                user.unlock_account()  # Déverrouiller le compte
                session.commit()

                self.logger.info(f"Mot de passe réinitialisé pour l'utilisateur {user.username}")
                return True

        except SQLAlchemyError as e:
            self.logger.error(f"Erreur lors de la réinitialisation du mot de passe: {e}")
            return False

    def assign_role(self, user_id: int, role_id: int) -> bool:
        """Assigne un rôle à un utilisateur"""
        try:
            with db_manager.get_session() as session:
                user = session.query(User).filter(User.id == user_id).first()
                role = session.query(Role).filter(Role.id == role_id).first()

                if not user or not role:
                    return False

                if role not in user.roles:
                    user.roles.append(role)
                    session.commit()
                    self.logger.info(f"Rôle {role.name} assigné à {user.username}")

                return True

        except SQLAlchemyError as e:
            self.logger.error(f"Erreur lors de l'assignation du rôle: {e}")
            return False

    def remove_role(self, user_id: int, role_id: int) -> bool:
        """Retire un rôle d'un utilisateur"""
        try:
            with db_manager.get_session() as session:
                user = session.query(User).filter(User.id == user_id).first()
                role = session.query(Role).filter(Role.id == role_id).first()

                if not user or not role:
                    return False

                if role in user.roles:
                    user.roles.remove(role)
                    session.commit()
                    self.logger.info(f"Rôle {role.name} retiré de {user.username}")

                return True

        except SQLAlchemyError as e:
            self.logger.error(f"Erreur lors du retrait du rôle: {e}")
            return False

    def get_user_permissions(self, user_id: int) -> List[str]:
        """Récupère toutes les permissions d'un utilisateur"""
        try:
            with db_manager.get_session() as session:
                user = session.query(User).filter(User.id == user_id).first()

                if not user:
                    return []

                if user.is_admin:
                    # Admin a toutes les permissions
                    all_permissions = session.query(Permission).all()
                    return [perm.name for perm in all_permissions]

                permissions = set()
                for role in user.roles:
                    if role.is_active:
                        for permission in role.permissions:
                            permissions.add(permission.name)

                return list(permissions)

        except SQLAlchemyError as e:
            self.logger.error(f"Erreur lors de la récupération des permissions: {e}")
            return []

    def validate_data(self, data: Dict[str, Any], is_update: bool = False) -> List[str]:
        """Valide les données utilisateur"""
        errors = super().validate_data(data, is_update)

        # Validation du nom d'utilisateur
        username = data.get('username')
        if username and len(username) < 3:
            errors.append("Le nom d'utilisateur doit contenir au moins 3 caractères")

        # Validation de l'email
        email = data.get('email')
        if email and '@' not in email:
            errors.append("L'adresse email n'est pas valide")

        # Validation du mot de passe (seulement pour création)
        if not is_update:
            password = data.get('password')
            if not password:
                errors.append("Le mot de passe est obligatoire")
            elif len(password) < 8:
                errors.append("Le mot de passe doit contenir au moins 8 caractères")

        return errors

class RoleService(BaseService):
    """Service pour la gestion des rôles"""

    def __init__(self):
        super().__init__(Role)

    def assign_permission(self, role_id: int, permission_id: int) -> bool:
        """Assigne une permission à un rôle"""
        try:
            with db_manager.get_session() as session:
                role = session.query(Role).filter(Role.id == role_id).first()
                permission = session.query(Permission).filter(Permission.id == permission_id).first()

                if not role or not permission:
                    return False

                if permission not in role.permissions:
                    role.permissions.append(permission)
                    session.commit()

                return True

        except SQLAlchemyError as e:
            self.logger.error(f"Erreur lors de l'assignation de la permission: {e}")
            return False

    def remove_permission(self, role_id: int, permission_id: int) -> bool:
        """Retire une permission d'un rôle"""
        try:
            with db_manager.get_session() as session:
                role = session.query(Role).filter(Role.id == role_id).first()
                permission = session.query(Permission).filter(Permission.id == permission_id).first()

                if not role or not permission:
                    return False

                if permission in role.permissions:
                    role.permissions.remove(permission)
                    session.commit()

                return True

        except SQLAlchemyError as e:
            self.logger.error(f"Erreur lors du retrait de la permission: {e}")
            return False

class PermissionService(BaseService):
    """Service pour la gestion des permissions"""

    def __init__(self):
        super().__init__(Permission)

    def get_by_module(self, module: str) -> List[Permission]:
        """Récupère les permissions par module"""
        return self.get_all(module=module)

    def create_default_permissions(self) -> bool:
        """Crée les permissions par défaut"""
        try:
            modules = ['commercial', 'stock', 'accounting', 'inventory', 'admin']
            actions = ['create', 'read', 'update', 'delete']

            with db_manager.get_session() as session:
                for module in modules:
                    for action in actions:
                        permission_name = f"{module}.{action}"

                        # Vérifier si la permission existe déjà
                        existing = session.query(Permission).filter(Permission.name == permission_name).first()
                        if not existing:
                            permission = Permission(
                                name=permission_name,
                                description=f"Permission {action} pour le module {module}",
                                module=module,
                                action=action
                            )
                            session.add(permission)

                session.commit()
                return True

        except SQLAlchemyError as e:
            self.logger.error(f"Erreur lors de la création des permissions par défaut: {e}")
            return False
