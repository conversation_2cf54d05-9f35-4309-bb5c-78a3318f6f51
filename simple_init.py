#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Script d'initialisation simple pour tester la base de données
"""

import sys
import os

# Ajouter le répertoire src au path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from src.core.logger import setup_logging
from src.dal.database import db_manager
from src.dal.models.user import User

def simple_init():
    """Initialisation simple"""
    print("🚀 Test d'initialisation simple...")
    
    # Configuration des logs
    setup_logging()
    
    try:
        # Créer les tables
        print("📊 Création des tables...")
        db_manager.create_tables()
        print("✅ Tables créées avec succès")
        
        # Test de connexion
        print("🔗 Test de connexion...")
        if db_manager.test_connection():
            print("✅ Connexion réussie")
        else:
            print("❌ Échec de connexion")
            return False
        
        # Créer un utilisateur simple
        print("👤 Création d'un utilisateur de test...")
        with db_manager.get_session() as session:
            # Vérifier si l'utilisateur admin existe déjà
            existing_user = session.query(User).filter(User.username == 'admin').first()
            if not existing_user:
                admin_user = User(
                    username='admin',
                    email='<EMAIL>',
                    first_name='Administrateur',
                    last_name='Système',
                    is_admin=True,
                    is_active=True
                )
                admin_user.set_password('admin123')
                session.add(admin_user)
                session.commit()
                print("✅ Utilisateur admin créé")
                print("   Nom d'utilisateur: admin")
                print("   Mot de passe: admin123")
            else:
                print("ℹ️ Utilisateur admin existe déjà")
        
        print("\n🎉 Initialisation simple terminée!")
        print("🚀 Vous pouvez maintenant tester l'application avec: python main.py")
        
    except Exception as e:
        print(f"❌ Erreur: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    return True

if __name__ == "__main__":
    simple_init()
