// qstylepainter.sip generated by MetaSIP
//
// This file is part of the QtWidgets Python extension module.
//
// Copyright (c) 2024 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt5.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


class QStylePainter : public QPainter
{
%TypeHeaderCode
#include <qstylepainter.h>
%End

public:
    QStylePainter();
    explicit QStylePainter(QWidget *w);
    QStylePainter(QPaintDevice *pd, QWidget *w);
    bool begin(QWidget *w);
    bool begin(QPaintDevice *pd, QWidget *w);
    QStyle *style() const;
    void drawPrimitive(QStyle::PrimitiveElement pe, const QStyleOption &opt);
    void drawControl(QStyle::ControlElement ce, const QStyleOption &opt);
    void drawComplexControl(QStyle::ComplexControl cc, const QStyleOptionComplex &opt);
    void drawItemText(const QRect &rect, int flags, const QPalette &pal, bool enabled, const QString &text, QPalette::ColorRole textRole = QPalette::NoRole);
    void drawItemPixmap(const QRect &r, int flags, const QPixmap &pixmap);
};
