// qprogressbar.sip generated by MetaSIP
//
// This file is part of the QtWidgets Python extension module.
//
// Copyright (c) 2024 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt5.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


class QProgressBar : public QWidget
{
%TypeHeaderCode
#include <qprogressbar.h>
%End

public:
    enum Direction
    {
        TopToBottom,
        BottomToTop,
    };

    explicit QProgressBar(QWidget *parent /TransferThis/ = 0);
    virtual ~QProgressBar();
    int minimum() const;
    int maximum() const;
    void setRange(int minimum, int maximum);
    int value() const;
    virtual QString text() const;
    void setTextVisible(bool visible);
    bool isTextVisible() const;
    Qt::Alignment alignment() const;
    void setAlignment(Qt::Alignment alignment);
    virtual QSize sizeHint() const;
    virtual QSize minimumSizeHint() const;
    Qt::Orientation orientation() const;
    void setInvertedAppearance(bool invert);
    void setTextDirection(QProgressBar::Direction textDirection);
    void setFormat(const QString &format);
    QString format() const;
%If (Qt_5_1_0 -)
    void resetFormat();
%End

public slots:
    void reset();
    void setMinimum(int minimum);
    void setMaximum(int maximum);
    void setValue(int value);
    void setOrientation(Qt::Orientation);

signals:
    void valueChanged(int value);

protected:
    void initStyleOption(QStyleOptionProgressBar *option) const;
    virtual bool event(QEvent *e);
    virtual void paintEvent(QPaintEvent *);
};
