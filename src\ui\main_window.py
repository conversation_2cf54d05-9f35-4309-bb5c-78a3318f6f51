#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Fenêtre principale de GSCOM
Interface moderne avec navigation latérale et modules
"""

import logging
from PyQt5.QtWidgets import *
from PyQt5.QtCore import *
from PyQt5.QtGui import *

from src.core.config import app_config

class MainWindow(QMainWindow):
    """Fenêtre principale de l'application GSCOM"""
    
    def __init__(self, user):
        super().__init__()
        self.current_user = user
        self.logger = logging.getLogger(__name__)
        
        self.init_ui()
        self.apply_styles()
        
        self.logger.info(f"Fenêtre principale ouverte pour l'utilisateur: {user.username}")
    
    def init_ui(self):
        """Initialise l'interface utilisateur"""
        # Configuration de la fenêtre
        self.setWindowTitle(f"GSCOM - {self.current_user.full_name}")
        self.setGeometry(100, 100, 1400, 900)
        
        # Widget central
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # Layout principal horizontal
        main_layout = QHBoxLayout(central_widget)
        main_layout.setContentsMargins(0, 0, 0, 0)
        main_layout.setSpacing(0)
        
        # Sidebar de navigation
        self.create_sidebar(main_layout)
        
        # Zone de contenu principal
        self.create_content_area(main_layout)
        
        # Barre de statut
        self.create_status_bar()
        
        # Menu principal
        self.create_menu_bar()
    
    def create_sidebar(self, main_layout):
        """Crée la barre latérale de navigation"""
        self.sidebar = QFrame()
        self.sidebar.setObjectName("sidebar")
        self.sidebar.setFixedWidth(250)
        
        sidebar_layout = QVBoxLayout(self.sidebar)
        sidebar_layout.setContentsMargins(0, 0, 0, 0)
        sidebar_layout.setSpacing(0)
        
        # Header du sidebar avec logo et nom
        header = QFrame()
        header.setObjectName("sidebarHeader")
        header.setFixedHeight(80)
        
        header_layout = QHBoxLayout(header)
        header_layout.setContentsMargins(20, 10, 20, 10)
        
        # Logo
        logo_label = QLabel("🏢")
        logo_label.setObjectName("logoLabel")
        logo_label.setFixedSize(40, 40)
        header_layout.addWidget(logo_label)
        
        # Titre
        title_layout = QVBoxLayout()
        title_label = QLabel("GSCOM")
        title_label.setObjectName("titleLabel")
        subtitle_label = QLabel("Gestion Commerciale")
        subtitle_label.setObjectName("subtitleLabel")
        title_layout.addWidget(title_label)
        title_layout.addWidget(subtitle_label)
        header_layout.addLayout(title_layout)
        
        sidebar_layout.addWidget(header)
        
        # Navigation
        nav_scroll = QScrollArea()
        nav_scroll.setWidgetResizable(True)
        nav_scroll.setHorizontalScrollBarPolicy(Qt.ScrollBarAlwaysOff)
        nav_scroll.setVerticalScrollBarPolicy(Qt.ScrollBarAsNeeded)
        nav_scroll.setObjectName("navScroll")
        
        nav_widget = QWidget()
        nav_layout = QVBoxLayout(nav_widget)
        nav_layout.setContentsMargins(0, 10, 0, 10)
        nav_layout.setSpacing(5)
        
        # Modules de navigation
        self.nav_buttons = {}
        modules = [
            ("dashboard", "📊", "Tableau de bord"),
            ("commercial", "💼", "Commercial"),
            ("clients", "👥", "Clients"),
            ("suppliers", "🏭", "Fournisseurs"),
            ("products", "📦", "Produits"),
            ("stock", "📋", "Stock"),
            ("inventory", "📝", "Inventaire"),
            ("accounting", "💰", "Comptabilité"),
            ("reports", "📈", "Rapports"),
            ("settings", "⚙️", "Paramètres"),
        ]
        
        for module_id, icon, title in modules:
            button = self.create_nav_button(module_id, icon, title)
            self.nav_buttons[module_id] = button
            nav_layout.addWidget(button)
        
        nav_layout.addStretch()
        
        # Informations utilisateur
        user_info = self.create_user_info()
        nav_layout.addWidget(user_info)
        
        nav_scroll.setWidget(nav_widget)
        sidebar_layout.addWidget(nav_scroll)
        
        main_layout.addWidget(self.sidebar)
    
    def create_nav_button(self, module_id, icon, title):
        """Crée un bouton de navigation"""
        button = QPushButton()
        button.setObjectName("navButton")
        button.setFixedHeight(50)
        button.setCursor(Qt.PointingHandCursor)
        
        # Layout du bouton
        layout = QHBoxLayout(button)
        layout.setContentsMargins(20, 10, 20, 10)
        layout.setSpacing(15)
        
        # Icône
        icon_label = QLabel(icon)
        icon_label.setObjectName("navIcon")
        icon_label.setFixedSize(24, 24)
        layout.addWidget(icon_label)
        
        # Titre
        title_label = QLabel(title)
        title_label.setObjectName("navTitle")
        layout.addWidget(title_label)
        
        layout.addStretch()
        
        # Connecter le signal
        button.clicked.connect(lambda: self.switch_module(module_id))
        
        return button
    
    def create_user_info(self):
        """Crée la zone d'informations utilisateur"""
        user_frame = QFrame()
        user_frame.setObjectName("userInfo")
        user_frame.setFixedHeight(80)
        
        layout = QVBoxLayout(user_frame)
        layout.setContentsMargins(20, 10, 20, 10)
        
        # Nom de l'utilisateur
        name_label = QLabel(self.current_user.full_name)
        name_label.setObjectName("userName")
        layout.addWidget(name_label)
        
        # Boutons d'action
        actions_layout = QHBoxLayout()
        
        profile_btn = QPushButton("👤")
        profile_btn.setObjectName("userActionButton")
        profile_btn.setFixedSize(30, 30)
        profile_btn.setToolTip("Profil")
        profile_btn.clicked.connect(self.show_profile)
        actions_layout.addWidget(profile_btn)
        
        logout_btn = QPushButton("🚪")
        logout_btn.setObjectName("userActionButton")
        logout_btn.setFixedSize(30, 30)
        logout_btn.setToolTip("Déconnexion")
        logout_btn.clicked.connect(self.logout)
        actions_layout.addWidget(logout_btn)
        
        actions_layout.addStretch()
        layout.addLayout(actions_layout)
        
        return user_frame
    
    def create_content_area(self, main_layout):
        """Crée la zone de contenu principal"""
        self.content_area = QFrame()
        self.content_area.setObjectName("contentArea")
        
        self.content_layout = QVBoxLayout(self.content_area)
        self.content_layout.setContentsMargins(20, 20, 20, 20)
        
        # Header de contenu
        self.content_header = QFrame()
        self.content_header.setObjectName("contentHeader")
        self.content_header.setFixedHeight(60)
        
        header_layout = QHBoxLayout(self.content_header)
        header_layout.setContentsMargins(0, 10, 0, 10)
        
        # Titre du module actuel
        self.module_title = QLabel("Tableau de bord")
        self.module_title.setObjectName("moduleTitle")
        header_layout.addWidget(self.module_title)
        
        header_layout.addStretch()
        
        # Boutons d'action rapide
        quick_actions = QHBoxLayout()
        
        new_btn = QPushButton("+ Nouveau")
        new_btn.setObjectName("quickActionButton")
        new_btn.clicked.connect(self.new_item)
        quick_actions.addWidget(new_btn)
        
        search_input = QLineEdit()
        search_input.setPlaceholderText("Rechercher...")
        search_input.setObjectName("searchInput")
        search_input.setFixedWidth(200)
        quick_actions.addWidget(search_input)
        
        header_layout.addLayout(quick_actions)
        
        self.content_layout.addWidget(self.content_header)
        
        # Zone de contenu dynamique
        self.content_stack = QStackedWidget()
        self.content_stack.setObjectName("contentStack")
        
        # Ajouter les différents modules
        self.create_dashboard()
        
        self.content_layout.addWidget(self.content_stack)
        
        main_layout.addWidget(self.content_area)
    
    def create_dashboard(self):
        """Crée le tableau de bord"""
        dashboard = QWidget()
        dashboard_layout = QVBoxLayout(dashboard)
        
        # Titre de bienvenue
        welcome_label = QLabel(f"Bienvenue, {self.current_user.first_name} !")
        welcome_label.setObjectName("welcomeLabel")
        dashboard_layout.addWidget(welcome_label)
        
        # Cartes de statistiques
        stats_layout = QHBoxLayout()
        
        # Carte Ventes
        sales_card = self.create_stat_card("💰", "Ventes du mois", "125,450 DA", "+12%")
        stats_layout.addWidget(sales_card)
        
        # Carte Clients
        clients_card = self.create_stat_card("👥", "Clients actifs", "1,234", "+5%")
        stats_layout.addWidget(clients_card)
        
        # Carte Stock
        stock_card = self.create_stat_card("📦", "Produits en stock", "5,678", "-2%")
        stats_layout.addWidget(stock_card)
        
        # Carte Commandes
        orders_card = self.create_stat_card("📋", "Commandes en cours", "89", "+8%")
        stats_layout.addWidget(orders_card)
        
        dashboard_layout.addLayout(stats_layout)
        
        # Zone de contenu supplémentaire
        content_area = QFrame()
        content_area.setObjectName("dashboardContent")
        content_layout = QVBoxLayout(content_area)
        
        info_label = QLabel("🎉 Application GSCOM initialisée avec succès !")
        info_label.setObjectName("infoLabel")
        info_label.setAlignment(Qt.AlignCenter)
        content_layout.addWidget(info_label)
        
        features_label = QLabel("""
        ✅ Base de données configurée
        ✅ Système d'authentification fonctionnel
        ✅ Interface moderne avec design futuriste
        ✅ Architecture modulaire (UI/BLL/DAL)
        ✅ Gestion des utilisateurs et permissions
        
        📋 Modules disponibles :
        • Gestion commerciale (devis, commandes, factures)
        • Gestion des stocks et inventaires
        • Comptabilité et finance
        • Rapports et tableaux de bord
        • Administration et paramètres
        """)
        features_label.setObjectName("featuresLabel")
        features_label.setAlignment(Qt.AlignCenter)
        content_layout.addWidget(features_label)
        
        dashboard_layout.addWidget(content_area)
        
        self.content_stack.addWidget(dashboard)
    
    def create_stat_card(self, icon, title, value, change):
        """Crée une carte de statistique"""
        card = QFrame()
        card.setObjectName("statCard")
        card.setFixedHeight(120)
        
        layout = QVBoxLayout(card)
        layout.setContentsMargins(20, 15, 20, 15)
        
        # Header avec icône et titre
        header_layout = QHBoxLayout()
        
        icon_label = QLabel(icon)
        icon_label.setObjectName("statIcon")
        header_layout.addWidget(icon_label)
        
        title_label = QLabel(title)
        title_label.setObjectName("statTitle")
        header_layout.addWidget(title_label)
        
        header_layout.addStretch()
        layout.addLayout(header_layout)
        
        # Valeur
        value_label = QLabel(value)
        value_label.setObjectName("statValue")
        layout.addWidget(value_label)
        
        # Changement
        change_label = QLabel(change)
        change_label.setObjectName("statChange")
        layout.addWidget(change_label)
        
        return card
    
    def create_status_bar(self):
        """Crée la barre de statut"""
        status_bar = self.statusBar()
        status_bar.setObjectName("statusBar")
        
        # Informations de connexion
        db_status = QLabel("🟢 Base de données connectée")
        status_bar.addWidget(db_status)
        
        status_bar.addPermanentWidget(QLabel(f"Utilisateur: {self.current_user.username}"))
        
        # Heure
        self.time_label = QLabel()
        self.update_time()
        status_bar.addPermanentWidget(self.time_label)
        
        # Timer pour mettre à jour l'heure
        self.timer = QTimer()
        self.timer.timeout.connect(self.update_time)
        self.timer.start(1000)  # Mise à jour chaque seconde
    
    def create_menu_bar(self):
        """Crée la barre de menu"""
        menubar = self.menuBar()
        menubar.setObjectName("menuBar")
        
        # Menu Fichier
        file_menu = menubar.addMenu("Fichier")
        file_menu.addAction("Nouveau", self.new_item, "Ctrl+N")
        file_menu.addSeparator()
        file_menu.addAction("Sauvegarder", self.save_data, "Ctrl+S")
        file_menu.addAction("Exporter", self.export_data, "Ctrl+E")
        file_menu.addSeparator()
        file_menu.addAction("Quitter", self.close, "Ctrl+Q")
        
        # Menu Édition
        edit_menu = menubar.addMenu("Édition")
        edit_menu.addAction("Préférences", self.show_preferences, "Ctrl+,")
        
        # Menu Aide
        help_menu = menubar.addMenu("Aide")
        help_menu.addAction("À propos", self.show_about)
        help_menu.addAction("Documentation", self.show_documentation, "F1")
    
    def apply_styles(self):
        """Applique les styles CSS"""
        style = """
        QMainWindow {
            background-color: #1a1a2e;
            color: #ffffff;
        }
        
        #sidebar {
            background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                stop:0 #16213e, stop:1 #0f1419);
            border-right: 1px solid #2a2a3e;
        }
        
        #sidebarHeader {
            background: rgba(0, 212, 255, 0.1);
            border-bottom: 1px solid #2a2a3e;
        }
        
        #logoLabel {
            font-size: 24px;
            background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                stop:0 #00d4ff, stop:1 #ff00ff);
            border-radius: 20px;
            text-align: center;
        }
        
        #titleLabel {
            font-size: 16px;
            font-weight: bold;
            color: #00d4ff;
        }
        
        #subtitleLabel {
            font-size: 11px;
            color: rgba(255, 255, 255, 0.7);
        }
        
        #navButton {
            background: transparent;
            border: none;
            text-align: left;
            padding: 10px;
            border-radius: 8px;
            margin: 2px 10px;
        }
        
        #navButton:hover {
            background: rgba(0, 212, 255, 0.1);
            border-left: 3px solid #00d4ff;
        }
        
        #navButton:pressed {
            background: rgba(0, 212, 255, 0.2);
        }
        
        #navIcon {
            font-size: 18px;
            color: #00d4ff;
        }
        
        #navTitle {
            font-size: 14px;
            color: #ffffff;
            font-weight: 500;
        }
        
        #userInfo {
            background: rgba(0, 0, 0, 0.2);
            border-top: 1px solid #2a2a3e;
            margin: 10px;
            border-radius: 8px;
        }
        
        #userName {
            font-size: 14px;
            font-weight: bold;
            color: #ffffff;
        }
        
        #userActionButton {
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 15px;
            font-size: 14px;
        }
        
        #userActionButton:hover {
            background: rgba(0, 212, 255, 0.2);
            border-color: #00d4ff;
        }
        
        #contentArea {
            background: #1a1a2e;
        }
        
        #contentHeader {
            background: rgba(255, 255, 255, 0.05);
            border-radius: 10px;
            margin-bottom: 10px;
        }
        
        #moduleTitle {
            font-size: 24px;
            font-weight: bold;
            color: #ffffff;
        }
        
        #quickActionButton {
            background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                stop:0 #00d4ff, stop:1 #ff00ff);
            border: none;
            border-radius: 6px;
            color: white;
            font-weight: bold;
            padding: 8px 16px;
        }
        
        #quickActionButton:hover {
            background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                stop:0 #00b8e6, stop:1 #e600e6);
        }
        
        #searchInput {
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 6px;
            padding: 8px 12px;
            color: white;
        }
        
        #searchInput:focus {
            border-color: #00d4ff;
            background: rgba(255, 255, 255, 0.15);
        }
        
        #welcomeLabel {
            font-size: 28px;
            font-weight: bold;
            color: #00d4ff;
            margin-bottom: 20px;
        }
        
        #statCard {
            background: rgba(255, 255, 255, 0.05);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 12px;
            margin: 5px;
        }
        
        #statCard:hover {
            background: rgba(255, 255, 255, 0.08);
            border-color: rgba(0, 212, 255, 0.3);
        }
        
        #statIcon {
            font-size: 20px;
            color: #00d4ff;
        }
        
        #statTitle {
            font-size: 12px;
            color: rgba(255, 255, 255, 0.8);
        }
        
        #statValue {
            font-size: 20px;
            font-weight: bold;
            color: #ffffff;
        }
        
        #statChange {
            font-size: 12px;
            color: #00ff88;
        }
        
        #dashboardContent {
            background: rgba(255, 255, 255, 0.03);
            border-radius: 12px;
            margin-top: 20px;
        }
        
        #infoLabel {
            font-size: 18px;
            font-weight: bold;
            color: #00d4ff;
            margin: 20px;
        }
        
        #featuresLabel {
            font-size: 14px;
            color: rgba(255, 255, 255, 0.9);
            line-height: 1.6;
        }
        
        #statusBar {
            background: rgba(0, 0, 0, 0.3);
            border-top: 1px solid #2a2a3e;
            color: rgba(255, 255, 255, 0.8);
        }
        
        #menuBar {
            background: rgba(0, 0, 0, 0.3);
            border-bottom: 1px solid #2a2a3e;
            color: #ffffff;
        }
        
        QMenuBar::item {
            background: transparent;
            padding: 8px 12px;
        }
        
        QMenuBar::item:selected {
            background: rgba(0, 212, 255, 0.2);
            border-radius: 4px;
        }
        
        QMenu {
            background: #2a2a3e;
            border: 1px solid #3a3a4e;
            color: #ffffff;
        }
        
        QMenu::item {
            padding: 8px 20px;
        }
        
        QMenu::item:selected {
            background: rgba(0, 212, 255, 0.2);
        }
        """
        
        self.setStyleSheet(style)
    
    def switch_module(self, module_id):
        """Change de module"""
        self.logger.info(f"Changement vers le module: {module_id}")
        
        # Mettre à jour le titre
        module_titles = {
            "dashboard": "Tableau de bord",
            "commercial": "Gestion Commerciale",
            "clients": "Gestion des Clients",
            "suppliers": "Gestion des Fournisseurs",
            "products": "Gestion des Produits",
            "stock": "Gestion des Stocks",
            "inventory": "Inventaire",
            "accounting": "Comptabilité",
            "reports": "Rapports",
            "settings": "Paramètres",
        }
        
        self.module_title.setText(module_titles.get(module_id, "Module"))
        
        # Réinitialiser les styles des boutons
        for btn in self.nav_buttons.values():
            btn.setStyleSheet("")
        
        # Mettre en surbrillance le bouton actuel
        if module_id in self.nav_buttons:
            self.nav_buttons[module_id].setStyleSheet("""
                #navButton {
                    background: rgba(0, 212, 255, 0.2);
                    border-left: 3px solid #00d4ff;
                }
            """)
    
    def update_time(self):
        """Met à jour l'affichage de l'heure"""
        from datetime import datetime
        current_time = datetime.now().strftime("%H:%M:%S")
        self.time_label.setText(current_time)
    
    def new_item(self):
        """Crée un nouvel élément"""
        QMessageBox.information(self, "Nouveau", "Fonctionnalité en cours de développement")
    
    def save_data(self):
        """Sauvegarde les données"""
        QMessageBox.information(self, "Sauvegarder", "Données sauvegardées")
    
    def export_data(self):
        """Exporte les données"""
        QMessageBox.information(self, "Exporter", "Fonctionnalité d'export en cours de développement")
    
    def show_preferences(self):
        """Affiche les préférences"""
        QMessageBox.information(self, "Préférences", "Fenêtre de préférences en cours de développement")
    
    def show_about(self):
        """Affiche les informations sur l'application"""
        QMessageBox.about(self, "À propos de GSCOM", 
                         "GSCOM v1.0.0\n\n"
                         "Application de Gestion Commerciale, Stock, Finance, Comptabilité et Inventaire\n\n"
                         "© 2024 GSCOM Solutions")
    
    def show_documentation(self):
        """Affiche la documentation"""
        QMessageBox.information(self, "Documentation", "Documentation en cours de rédaction")
    
    def show_profile(self):
        """Affiche le profil utilisateur"""
        QMessageBox.information(self, "Profil", f"Profil de {self.current_user.full_name}")
    
    def logout(self):
        """Déconnexion"""
        reply = QMessageBox.question(self, "Déconnexion", 
                                   "Êtes-vous sûr de vouloir vous déconnecter ?",
                                   QMessageBox.Yes | QMessageBox.No)
        
        if reply == QMessageBox.Yes:
            self.logger.info(f"Déconnexion de l'utilisateur: {self.current_user.username}")
            self.close()
            
            # Relancer la fenêtre de connexion
            from src.ui.login_window import LoginWindow
            self.login_window = LoginWindow()
            self.login_window.show()
    
    def closeEvent(self, event):
        """Événement de fermeture"""
        self.timer.stop()
        event.accept()
