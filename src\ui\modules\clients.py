#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Module de gestion des clients
Interface complète pour la gestion de la base clients
"""

import logging
from PyQt5.QtWidgets import *
from PyQt5.QtCore import *
from PyQt5.QtGui import *

from src.ui.components.base_widget import ModuleWidget, FormDialog
from src.bll.user_service import UserService
from src.dal.models.client import Client, ClientType, PaymentTerms
from src.dal.database import db_manager

class ClientsWidget(ModuleWidget):
    """Widget principal pour la gestion des clients"""

    def __init__(self, parent=None):
        super().__init__("Gestion des Clients", parent)
        self.user_service = UserService()
        self.clients = []
        self.load_data()

    def create_data_table(self):
        """Crée le tableau des clients"""
        super().create_data_table()

        # Configuration des colonnes
        headers = ["Code", "Nom", "Type", "Email", "Téléphone", "Ville", "Statut"]
        self.data_table.setColumnCount(len(headers))
        self.data_table.setHorizontalHeaderLabels(headers)

        # Ajuster la largeur des colonnes
        header = self.data_table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.ResizeToContents)  # Code
        header.setSectionResizeMode(1, QHeaderView.Stretch)           # Nom
        header.setSectionResizeMode(2, QHeaderView.ResizeToContents)  # Type
        header.setSectionResizeMode(3, QHeaderView.Stretch)           # Email
        header.setSectionResizeMode(4, QHeaderView.ResizeToContents)  # Téléphone
        header.setSectionResizeMode(5, QHeaderView.ResizeToContents)  # Ville
        header.setSectionResizeMode(6, QHeaderView.ResizeToContents)  # Statut

        # Menu contextuel
        self.data_table.setContextMenuPolicy(Qt.CustomContextMenu)
        self.data_table.customContextMenuRequested.connect(self.show_context_menu)

    def load_data(self):
        """Charge les données des clients"""
        try:
            with db_manager.get_session() as session:
                clients = session.query(Client).all()

                # Créer des copies détachées des objets
                self.clients = []
                for client in clients:
                    self.clients.append(client)

                # Détacher les objets de la session
                session.expunge_all()

                self.populate_table()
                self.update_count(len(self.clients))
                self.show_message(f"{len(self.clients)} clients chargés", "success")
        except Exception as e:
            self.logger.error(f"Erreur lors du chargement des clients: {e}")
            self.show_message("Erreur lors du chargement", "error")

    def populate_table(self):
        """Remplit le tableau avec les données"""
        self.data_table.setRowCount(len(self.clients))

        for row, client in enumerate(self.clients):
            # Code
            self.data_table.setItem(row, 0, QTableWidgetItem(client.code))

            # Nom
            self.data_table.setItem(row, 1, QTableWidgetItem(client.display_name))

            # Type
            type_text = "Entreprise" if client.client_type == ClientType.COMPANY else "Particulier"
            self.data_table.setItem(row, 2, QTableWidgetItem(type_text))

            # Email
            self.data_table.setItem(row, 3, QTableWidgetItem(client.email or ""))

            # Téléphone
            self.data_table.setItem(row, 4, QTableWidgetItem(client.phone or ""))

            # Ville
            self.data_table.setItem(row, 5, QTableWidgetItem(client.city or ""))

            # Statut
            status_text = "Actif" if client.is_active else "Inactif"
            status_item = QTableWidgetItem(status_text)
            if client.is_active:
                status_item.setForeground(QColor("#00ff88"))
            else:
                status_item.setForeground(QColor("#ff4444"))
            self.data_table.setItem(row, 6, status_item)

    def get_item_from_row(self, row):
        """Récupère le client depuis la ligne sélectionnée"""
        if 0 <= row < len(self.clients):
            return self.clients[row]
        return None

    def handle_action(self, action):
        """Gère les actions du module"""
        if action == "new":
            self.new_client()
        elif action == "edit" and self.current_item:
            self.edit_client(self.current_item)
        elif action == "delete" and self.current_item:
            self.delete_client(self.current_item)

        super().handle_action(action)

    def new_client(self):
        """Crée un nouveau client"""
        dialog = ClientFormDialog("Nouveau Client", self)
        if dialog.exec_() == QDialog.Accepted:
            client_data = dialog.get_data()
            if self.save_client(client_data):
                self.refresh_data()

    def edit_client(self, client):
        """Modifie un client existant"""
        dialog = ClientFormDialog("Modifier Client", self)
        dialog.set_data(client)
        if dialog.exec_() == QDialog.Accepted:
            client_data = dialog.get_data()
            client_data['id'] = client.id
            if self.save_client(client_data, is_edit=True):
                self.refresh_data()

    def delete_client(self, client):
        """Supprime un client"""
        reply = QMessageBox.question(
            self,
            "Confirmer la suppression",
            f"Êtes-vous sûr de vouloir supprimer le client '{client.display_name}' ?\n\n"
            "Cette action est irréversible.",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )

        if reply == QMessageBox.Yes:
            try:
                with db_manager.get_session() as session:
                    client_to_delete = session.query(Client).filter(Client.id == client.id).first()
                    if client_to_delete:
                        session.delete(client_to_delete)
                        session.commit()
                        self.show_message(f"Client '{client.display_name}' supprimé", "success")
                        self.refresh_data()
            except Exception as e:
                self.logger.error(f"Erreur lors de la suppression: {e}")
                self.show_message("Erreur lors de la suppression", "error")

    def save_client(self, data, is_edit=False):
        """Sauvegarde un client"""
        try:
            with db_manager.get_session() as session:
                if is_edit:
                    client = session.query(Client).filter(Client.id == data['id']).first()
                    if not client:
                        self.show_message("Client introuvable", "error")
                        return False

                    # Mettre à jour les champs
                    for key, value in data.items():
                        if key != 'id' and hasattr(client, key):
                            setattr(client, key, value)
                else:
                    # Générer un code client automatique
                    if not data.get('code'):
                        last_client = session.query(Client).order_by(Client.id.desc()).first()
                        next_id = (last_client.id + 1) if last_client else 1
                        data['code'] = f"CLI{next_id:06d}"

                    client = Client(**data)
                    session.add(client)

                session.commit()
                action = "modifié" if is_edit else "créé"
                self.show_message(f"Client {action} avec succès", "success")
                return True

        except Exception as e:
            self.logger.error(f"Erreur lors de la sauvegarde: {e}")
            self.show_message("Erreur lors de la sauvegarde", "error")
            return False

    def show_context_menu(self, position):
        """Affiche le menu contextuel"""
        if self.data_table.itemAt(position):
            menu = QMenu(self)

            edit_action = menu.addAction("✏️ Modifier")
            edit_action.triggered.connect(lambda: self.handle_action("edit"))

            delete_action = menu.addAction("🗑️ Supprimer")
            delete_action.triggered.connect(lambda: self.handle_action("delete"))

            menu.addSeparator()

            view_action = menu.addAction("👁️ Voir détails")
            view_action.triggered.connect(self.view_client_details)

            menu.exec_(self.data_table.mapToGlobal(position))

    def view_client_details(self):
        """Affiche les détails du client"""
        if self.current_item:
            dialog = ClientDetailsDialog(self.current_item, self)
            dialog.exec_()

class ClientFormDialog(FormDialog):
    """Formulaire de saisie/modification client"""

    def __init__(self, title, parent=None):
        super().__init__(title, parent)
        self.setup_form()

    def setup_form(self):
        """Configure le formulaire"""
        # Informations de base
        self.code_input = QLineEdit()
        self.code_input.setPlaceholderText("Généré automatiquement si vide")
        self.add_field("Code:", self.code_input)

        self.name_input = QLineEdit()
        self.name_input.setPlaceholderText("Nom du client")
        self.add_field("Nom *:", self.name_input)

        self.company_input = QLineEdit()
        self.company_input.setPlaceholderText("Nom de l'entreprise")
        self.add_field("Entreprise:", self.company_input)

        self.type_combo = QComboBox()
        self.type_combo.addItems(["Particulier", "Entreprise", "Administration"])
        self.add_field("Type:", self.type_combo)

        # Contact
        self.email_input = QLineEdit()
        self.email_input.setPlaceholderText("<EMAIL>")
        self.add_field("Email:", self.email_input)

        self.phone_input = QLineEdit()
        self.phone_input.setPlaceholderText("+213 XXX XXX XXX")
        self.add_field("Téléphone:", self.phone_input)

        self.mobile_input = QLineEdit()
        self.mobile_input.setPlaceholderText("+213 XXX XXX XXX")
        self.add_field("Mobile:", self.mobile_input)

        # Adresse
        self.address_input = QTextEdit()
        self.address_input.setMaximumHeight(60)
        self.address_input.setPlaceholderText("Adresse complète")
        self.add_field("Adresse:", self.address_input)

        self.city_input = QLineEdit()
        self.city_input.setPlaceholderText("Ville")
        self.add_field("Ville:", self.city_input)

        self.postal_code_input = QLineEdit()
        self.postal_code_input.setPlaceholderText("Code postal")
        self.add_field("Code postal:", self.postal_code_input)

        # Informations commerciales
        self.tax_id_input = QLineEdit()
        self.tax_id_input.setPlaceholderText("Numéro NIF")
        self.add_field("NIF:", self.tax_id_input)

        self.payment_terms_combo = QComboBox()
        self.payment_terms_combo.addItems(["Comptant", "30 jours", "60 jours", "90 jours"])
        self.add_field("Conditions paiement:", self.payment_terms_combo)

        self.credit_limit_input = QDoubleSpinBox()
        self.credit_limit_input.setMaximum(*********.99)
        self.credit_limit_input.setSuffix(" DA")
        self.add_field("Limite de crédit:", self.credit_limit_input)

        # Statut
        self.active_checkbox = QCheckBox("Client actif")
        self.active_checkbox.setChecked(True)
        self.add_field("", self.active_checkbox)

        self.vip_checkbox = QCheckBox("Client VIP")
        self.add_field("", self.vip_checkbox)

        # Notes
        self.notes_input = QTextEdit()
        self.notes_input.setMaximumHeight(80)
        self.notes_input.setPlaceholderText("Notes et commentaires...")
        self.add_field("Notes:", self.notes_input)

    def get_data(self):
        """Récupère les données du formulaire"""
        # Mapping des types
        type_mapping = {
            "Particulier": ClientType.INDIVIDUAL,
            "Entreprise": ClientType.COMPANY,
            "Administration": ClientType.GOVERNMENT
        }

        payment_mapping = {
            "Comptant": PaymentTerms.CASH,
            "30 jours": PaymentTerms.NET_30,
            "60 jours": PaymentTerms.NET_60,
            "90 jours": PaymentTerms.NET_90
        }

        return {
            'code': self.code_input.text().strip() or None,
            'name': self.name_input.text().strip(),
            'company_name': self.company_input.text().strip() or None,
            'client_type': type_mapping[self.type_combo.currentText()],
            'email': self.email_input.text().strip() or None,
            'phone': self.phone_input.text().strip() or None,
            'mobile': self.mobile_input.text().strip() or None,
            'address': self.address_input.toPlainText().strip() or None,
            'city': self.city_input.text().strip() or None,
            'postal_code': self.postal_code_input.text().strip() or None,
            'tax_id': self.tax_id_input.text().strip() or None,
            'payment_terms': payment_mapping[self.payment_terms_combo.currentText()],
            'credit_limit': self.credit_limit_input.value(),
            'is_active': self.active_checkbox.isChecked(),
            'is_vip': self.vip_checkbox.isChecked(),
            'notes': self.notes_input.toPlainText().strip() or None
        }

    def set_data(self, client):
        """Remplit le formulaire avec les données du client"""
        self.code_input.setText(client.code or "")
        self.name_input.setText(client.name or "")
        self.company_input.setText(client.company_name or "")

        # Type
        type_text = {
            ClientType.INDIVIDUAL: "Particulier",
            ClientType.COMPANY: "Entreprise",
            ClientType.GOVERNMENT: "Administration"
        }.get(client.client_type, "Particulier")
        self.type_combo.setCurrentText(type_text)

        self.email_input.setText(client.email or "")
        self.phone_input.setText(client.phone or "")
        self.mobile_input.setText(client.mobile or "")
        self.address_input.setPlainText(client.address or "")
        self.city_input.setText(client.city or "")
        self.postal_code_input.setText(client.postal_code or "")
        self.tax_id_input.setText(client.tax_id or "")

        # Conditions de paiement
        payment_text = {
            PaymentTerms.CASH: "Comptant",
            PaymentTerms.NET_30: "30 jours",
            PaymentTerms.NET_60: "60 jours",
            PaymentTerms.NET_90: "90 jours"
        }.get(client.payment_terms, "Comptant")
        self.payment_terms_combo.setCurrentText(payment_text)

        self.credit_limit_input.setValue(float(client.credit_limit or 0))
        self.active_checkbox.setChecked(client.is_active)
        self.vip_checkbox.setChecked(client.is_vip)
        self.notes_input.setPlainText(client.notes or "")

class ClientDetailsDialog(QDialog):
    """Dialogue d'affichage des détails client"""

    def __init__(self, client, parent=None):
        super().__init__(parent)
        self.client = client
        self.setWindowTitle(f"Détails - {client.display_name}")
        self.setFixedSize(600, 500)
        self.setup_ui()
        self.apply_styles()

    def setup_ui(self):
        """Configure l'interface"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(20, 20, 20, 20)

        # En-tête
        header_layout = QHBoxLayout()

        icon_label = QLabel("👤")
        icon_label.setStyleSheet("font-size: 32px;")
        header_layout.addWidget(icon_label)

        title_layout = QVBoxLayout()
        title_label = QLabel(self.client.display_name)
        title_label.setStyleSheet("font-size: 18px; font-weight: bold; color: #00d4ff;")
        title_layout.addWidget(title_label)

        subtitle_label = QLabel(f"Code: {self.client.code}")
        subtitle_label.setStyleSheet("color: rgba(255, 255, 255, 0.7);")
        title_layout.addWidget(subtitle_label)

        header_layout.addLayout(title_layout)
        header_layout.addStretch()

        layout.addLayout(header_layout)

        # Contenu dans un scroll area
        scroll = QScrollArea()
        scroll.setWidgetResizable(True)

        content_widget = QWidget()
        content_layout = QVBoxLayout(content_widget)

        # Informations générales
        self.add_section(content_layout, "Informations Générales", [
            ("Type", "Entreprise" if self.client.client_type == ClientType.COMPANY else "Particulier"),
            ("Statut", "Actif" if self.client.is_active else "Inactif"),
            ("VIP", "Oui" if self.client.is_vip else "Non"),
        ])

        # Contact
        contact_info = []
        if self.client.email:
            contact_info.append(("Email", self.client.email))
        if self.client.phone:
            contact_info.append(("Téléphone", self.client.phone))
        if self.client.mobile:
            contact_info.append(("Mobile", self.client.mobile))

        if contact_info:
            self.add_section(content_layout, "Contact", contact_info)

        # Adresse
        if self.client.full_address:
            self.add_section(content_layout, "Adresse", [
                ("Adresse complète", self.client.full_address)
            ])

        # Informations commerciales
        commercial_info = []
        if self.client.tax_id:
            commercial_info.append(("NIF", self.client.tax_id))
        if self.client.credit_limit:
            commercial_info.append(("Limite de crédit", f"{self.client.credit_limit} DA"))

        payment_terms = {
            PaymentTerms.CASH: "Comptant",
            PaymentTerms.NET_30: "30 jours",
            PaymentTerms.NET_60: "60 jours",
            PaymentTerms.NET_90: "90 jours"
        }.get(self.client.payment_terms, "Non défini")
        commercial_info.append(("Conditions de paiement", payment_terms))

        self.add_section(content_layout, "Informations Commerciales", commercial_info)

        # Notes
        if self.client.notes:
            self.add_section(content_layout, "Notes", [
                ("", self.client.notes)
            ])

        scroll.setWidget(content_widget)
        layout.addWidget(scroll)

        # Bouton fermer
        close_button = QPushButton("Fermer")
        close_button.clicked.connect(self.accept)
        layout.addWidget(close_button)

    def add_section(self, layout, title, items):
        """Ajoute une section d'informations"""
        # Titre de section
        title_label = QLabel(title)
        title_label.setStyleSheet("""
            font-size: 14px;
            font-weight: bold;
            color: #00d4ff;
            margin-top: 15px;
            margin-bottom: 5px;
        """)
        layout.addWidget(title_label)

        # Contenu de la section
        for label, value in items:
            item_layout = QHBoxLayout()

            if label:
                label_widget = QLabel(f"{label}:")
                label_widget.setStyleSheet("color: rgba(255, 255, 255, 0.8); font-weight: 500;")
                label_widget.setFixedWidth(150)
                item_layout.addWidget(label_widget)

            value_widget = QLabel(str(value))
            value_widget.setStyleSheet("color: white;")
            value_widget.setWordWrap(True)
            item_layout.addWidget(value_widget)

            layout.addLayout(item_layout)

    def apply_styles(self):
        """Applique les styles"""
        self.setStyleSheet("""
            QDialog {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(10, 10, 30, 0.95),
                    stop:1 rgba(20, 20, 50, 0.95));
                border: 1px solid rgba(255, 255, 255, 0.2);
                border-radius: 12px;
            }

            QScrollArea {
                border: none;
                background: transparent;
            }

            QPushButton {
                background: rgba(255, 255, 255, 0.1);
                border: 1px solid rgba(255, 255, 255, 0.2);
                border-radius: 6px;
                padding: 10px 20px;
                color: white;
                font-weight: 500;
            }

            QPushButton:hover {
                background: rgba(0, 212, 255, 0.2);
                border-color: #00d4ff;
            }
        """)
