#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Script d'initialisation de la base de données GSCOM
Crée les tables et les données de base
"""

import sys
import os
import logging

# Ajouter le répertoire src au path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from src.core.logger import setup_logging
from src.dal.database import db_manager
from src.bll.user_service import UserService, RoleService, PermissionService

def init_database():
    """Initialise la base de données"""
    print("🚀 Initialisation de la base de données GSCOM...")
    
    # Configuration des logs
    setup_logging()
    logger = logging.getLogger(__name__)
    
    try:
        # Créer les tables
        print("📊 Création des tables...")
        db_manager.create_tables()
        print("✅ Tables créées avec succès")
        
        # Créer les permissions par défaut
        print("🔐 Création des permissions par défaut...")
        permission_service = PermissionService()
        if permission_service.create_default_permissions():
            print("✅ Permissions créées avec succès")
        else:
            print("⚠️ Erreur lors de la création des permissions")
        
        # Créer le rôle administrateur
        print("👑 Création du rôle administrateur...")
        role_service = RoleService()
        admin_role = role_service.create({
            'name': 'Administrateur',
            'description': 'Accès complet à toutes les fonctionnalités',
            'is_active': True
        })
        
        if admin_role:
            print("✅ Rôle administrateur créé")
            
            # Assigner toutes les permissions au rôle admin
            permissions = permission_service.get_all()
            for permission in permissions:
                role_service.assign_permission(admin_role.id, permission.id)
            print("✅ Permissions assignées au rôle administrateur")
        
        # Créer l'utilisateur administrateur par défaut
        print("👤 Création de l'utilisateur administrateur...")
        user_service = UserService()
        admin_user = user_service.create_user({
            'username': 'admin',
            'email': '<EMAIL>',
            'password': 'admin123',
            'first_name': 'Administrateur',
            'last_name': 'Système',
            'is_admin': True,
            'is_active': True
        })
        
        if admin_user and admin_role:
            user_service.assign_role(admin_user.id, admin_role.id)
            print("✅ Utilisateur administrateur créé")
            print("   Nom d'utilisateur: admin")
            print("   Mot de passe: admin123")
        
        # Créer un utilisateur de démonstration
        print("👤 Création d'un utilisateur de démonstration...")
        demo_user = user_service.create_user({
            'username': 'demo',
            'email': '<EMAIL>',
            'password': 'demo123',
            'first_name': 'Utilisateur',
            'last_name': 'Démonstration',
            'is_admin': False,
            'is_active': True
        })
        
        if demo_user:
            print("✅ Utilisateur de démonstration créé")
            print("   Nom d'utilisateur: demo")
            print("   Mot de passe: demo123")
        
        print("\n🎉 Initialisation terminée avec succès!")
        print("\n📋 Résumé:")
        print("   - Base de données créée")
        print("   - Tables initialisées")
        print("   - Permissions configurées")
        print("   - Utilisateurs créés")
        print("\n🚀 Vous pouvez maintenant lancer l'application avec: python main.py")
        
    except Exception as e:
        logger.error(f"Erreur lors de l'initialisation: {e}")
        print(f"❌ Erreur: {e}")
        return False
    
    return True

if __name__ == "__main__":
    init_database()
