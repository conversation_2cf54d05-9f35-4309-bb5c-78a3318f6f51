#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Script de mise à jour et d'amélioration de GSCOM
Ajoute des fonctionnalités et optimisations
"""

import sys
import os

# Ajouter le répertoire src au path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from src.dal.database import db_manager
from src.dal.models.product import Product, Category, Unit, ProductType, ProductStatus
from src.dal.models.client import Client, Supplier, ClientType, PaymentTerms

def create_sample_products():
    """Crée des produits d'exemple"""
    print("📦 Création de produits d'exemple...")
    
    try:
        with db_manager.get_session() as session:
            # Récupérer les catégories et unités
            categories = {cat.code: cat.id for cat in session.query(Category).all()}
            units = {unit.code: unit.id for unit in session.query(Unit).all()}
            
            products_data = [
                {
                    "code": "PRD001",
                    "name": "Ordinateur Portable Dell",
                    "description": "Ordinateur portable Dell Inspiron 15, 8GB RAM, 256GB SSD",
                    "category_id": categories.get("CAT001"),  # Électronique
                    "unit_id": units.get("PC"),  # Pièce
                    "product_type": ProductType.PRODUCT,
                    "status": ProductStatus.ACTIVE,
                    "purchase_price": 85000.00,
                    "sale_price": 120000.00,
                    "tax_rate": 19.0,
                    "track_stock": True,
                    "current_stock": 15,
                    "min_stock": 5,
                    "max_stock": 50
                },
                {
                    "code": "PRD002",
                    "name": "Bureau en Bois",
                    "description": "Bureau en bois massif 120x60cm avec tiroirs",
                    "category_id": categories.get("CAT002"),  # Mobilier
                    "unit_id": units.get("PC"),
                    "product_type": ProductType.PRODUCT,
                    "status": ProductStatus.ACTIVE,
                    "purchase_price": 25000.00,
                    "sale_price": 35000.00,
                    "tax_rate": 19.0,
                    "track_stock": True,
                    "current_stock": 8,
                    "min_stock": 2,
                    "max_stock": 20
                },
                {
                    "code": "PRD003",
                    "name": "Ramette Papier A4",
                    "description": "Ramette de papier A4 80g, 500 feuilles",
                    "category_id": categories.get("CAT003"),  # Fournitures
                    "unit_id": units.get("PAQ"),  # Paquet
                    "product_type": ProductType.CONSUMABLE,
                    "status": ProductStatus.ACTIVE,
                    "purchase_price": 800.00,
                    "sale_price": 1200.00,
                    "tax_rate": 19.0,
                    "track_stock": True,
                    "current_stock": 150,
                    "min_stock": 50,
                    "max_stock": 500
                },
                {
                    "code": "PRD004",
                    "name": "Consultation Informatique",
                    "description": "Service de consultation et assistance informatique",
                    "category_id": categories.get("CAT001"),  # Électronique
                    "unit_id": units.get("PC"),  # Pièce (heure)
                    "product_type": ProductType.SERVICE,
                    "status": ProductStatus.ACTIVE,
                    "purchase_price": 0.00,
                    "sale_price": 5000.00,
                    "tax_rate": 19.0,
                    "track_stock": False,
                    "current_stock": 0,
                    "min_stock": 0,
                    "max_stock": 0
                },
                {
                    "code": "PRD005",
                    "name": "Smartphone Samsung",
                    "description": "Smartphone Samsung Galaxy A54, 128GB",
                    "category_id": categories.get("CAT001"),  # Électronique
                    "unit_id": units.get("PC"),
                    "product_type": ProductType.PRODUCT,
                    "status": ProductStatus.ACTIVE,
                    "purchase_price": 45000.00,
                    "sale_price": 65000.00,
                    "tax_rate": 19.0,
                    "track_stock": True,
                    "current_stock": 3,  # Stock bas
                    "min_stock": 5,
                    "max_stock": 30
                },
                {
                    "code": "PRD006",
                    "name": "Chaise de Bureau",
                    "description": "Chaise de bureau ergonomique avec accoudoirs",
                    "category_id": categories.get("CAT002"),  # Mobilier
                    "unit_id": units.get("PC"),
                    "product_type": ProductType.PRODUCT,
                    "status": ProductStatus.ACTIVE,
                    "purchase_price": 12000.00,
                    "sale_price": 18000.00,
                    "tax_rate": 19.0,
                    "track_stock": True,
                    "current_stock": 0,  # Rupture de stock
                    "min_stock": 3,
                    "max_stock": 25
                }
            ]
            
            for product_data in products_data:
                # Vérifier si le produit existe déjà
                existing = session.query(Product).filter(Product.code == product_data["code"]).first()
                if not existing:
                    product = Product(**product_data)
                    session.add(product)
            
            session.commit()
            print("✅ Produits d'exemple créés")
            
    except Exception as e:
        print(f"❌ Erreur lors de la création des produits: {e}")

def create_sample_suppliers():
    """Crée des fournisseurs d'exemple"""
    print("🏭 Création de fournisseurs d'exemple...")
    
    try:
        with db_manager.get_session() as session:
            suppliers_data = [
                {
                    "code": "FOU001",
                    "name": "Karim Techno",
                    "company_name": "Karim Techno SARL",
                    "email": "<EMAIL>",
                    "phone": "+*********** 222",
                    "city": "Alger",
                    "tax_id": "111222333444555",
                    "payment_terms": PaymentTerms.NET_30,
                    "is_active": True,
                    "is_preferred": True
                },
                {
                    "code": "FOU002",
                    "name": "Mobilier Plus",
                    "company_name": "Mobilier Plus SPA",
                    "email": "<EMAIL>",
                    "phone": "+*********** 444",
                    "city": "Oran",
                    "tax_id": "222333444555666",
                    "payment_terms": PaymentTerms.NET_60,
                    "minimum_order": 10000.00,
                    "delivery_time": 7,
                    "is_active": True,
                    "is_preferred": False
                },
                {
                    "code": "FOU003",
                    "name": "Papeterie Centrale",
                    "company_name": "Papeterie Centrale EURL",
                    "email": "<EMAIL>",
                    "phone": "+*********** 666",
                    "city": "Constantine",
                    "tax_id": "333444555666777",
                    "payment_terms": PaymentTerms.CASH,
                    "minimum_order": 5000.00,
                    "delivery_time": 3,
                    "is_active": True,
                    "is_preferred": True
                }
            ]
            
            for supplier_data in suppliers_data:
                # Vérifier si le fournisseur existe déjà
                existing = session.query(Supplier).filter(Supplier.code == supplier_data["code"]).first()
                if not existing:
                    supplier = Supplier(**supplier_data)
                    session.add(supplier)
            
            session.commit()
            print("✅ Fournisseurs d'exemple créés")
            
    except Exception as e:
        print(f"❌ Erreur lors de la création des fournisseurs: {e}")

def update_database_schema():
    """Met à jour le schéma de base de données si nécessaire"""
    print("🔄 Vérification du schéma de base de données...")
    
    try:
        # Créer toutes les tables (ne fait rien si elles existent déjà)
        db_manager.create_tables()
        print("✅ Schéma de base de données à jour")
        
    except Exception as e:
        print(f"❌ Erreur lors de la mise à jour du schéma: {e}")

def optimize_database():
    """Optimise la base de données"""
    print("⚡ Optimisation de la base de données...")
    
    try:
        with db_manager.get_session() as session:
            # Exécuter VACUUM pour SQLite (optimise la base)
            if db_manager.engine.dialect.name == 'sqlite':
                session.execute("VACUUM")
                session.commit()
                print("✅ Base de données optimisée (VACUUM)")
            else:
                print("ℹ️ Optimisation non nécessaire pour ce type de base")
                
    except Exception as e:
        print(f"❌ Erreur lors de l'optimisation: {e}")

def show_statistics():
    """Affiche les statistiques de la base de données"""
    print("\n📊 Statistiques de la base de données:")
    
    try:
        with db_manager.get_session() as session:
            # Compter les enregistrements
            clients_count = session.query(Client).count()
            suppliers_count = session.query(Supplier).count()
            products_count = session.query(Product).count()
            categories_count = session.query(Category).count()
            units_count = session.query(Unit).count()
            
            print(f"   👥 Clients: {clients_count}")
            print(f"   🏭 Fournisseurs: {suppliers_count}")
            print(f"   📦 Produits: {products_count}")
            print(f"   📂 Catégories: {categories_count}")
            print(f"   📏 Unités: {units_count}")
            
            # Statistiques produits
            active_products = session.query(Product).filter(Product.status == ProductStatus.ACTIVE).count()
            products_with_stock = session.query(Product).filter(
                Product.track_stock == True,
                Product.current_stock > 0
            ).count()
            low_stock_products = session.query(Product).filter(
                Product.track_stock == True,
                Product.current_stock <= Product.min_stock,
                Product.current_stock > 0
            ).count()
            out_of_stock_products = session.query(Product).filter(
                Product.track_stock == True,
                Product.current_stock <= 0
            ).count()
            
            print(f"\n   📈 Produits actifs: {active_products}")
            print(f"   📦 Produits en stock: {products_with_stock}")
            print(f"   ⚠️ Stock bas: {low_stock_products}")
            print(f"   ❌ Ruptures: {out_of_stock_products}")
            
    except Exception as e:
        print(f"❌ Erreur lors du calcul des statistiques: {e}")

def main():
    """Fonction principale de mise à jour"""
    print("🚀 Mise à jour de GSCOM...")
    print("=" * 50)
    
    try:
        # Mise à jour du schéma
        update_database_schema()
        
        # Création des données d'exemple
        create_sample_products()
        create_sample_suppliers()
        
        # Optimisation
        optimize_database()
        
        # Affichage des statistiques
        show_statistics()
        
        print("\n" + "=" * 50)
        print("🎉 Mise à jour terminée avec succès !")
        print("\n💡 Nouvelles fonctionnalités disponibles :")
        print("   • Produits d'exemple avec différents types")
        print("   • Fournisseurs d'exemple")
        print("   • Gestion des stocks avec alertes")
        print("   • Tableau de bord avec statistiques réelles")
        print("   • Interface clients et produits complète")
        
        print("\n🚀 Relancez l'application pour voir les améliorations :")
        print("   python main.py")
        
    except Exception as e:
        print(f"\n❌ Erreur lors de la mise à jour: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
