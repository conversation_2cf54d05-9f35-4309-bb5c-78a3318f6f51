// qaudioinputselectorcontrol.sip generated by MetaSIP
//
// This file is part of the QtMultimedia Python extension module.
//
// Copyright (c) 2024 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt5.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


class QAudioInputSelectorControl : public QMediaControl
{
%TypeHeaderCode
#include <qaudioinputselectorcontrol.h>
%End

public:
    virtual ~QAudioInputSelectorControl();
    virtual QList<QString> availableInputs() const = 0;
    virtual QString inputDescription(const QString &name) const = 0;
    virtual QString defaultInput() const = 0;
    virtual QString activeInput() const = 0;

public slots:
    virtual void setActiveInput(const QString &name) = 0;

signals:
    void activeInputChanged(const QString &name);
    void availableInputsChanged();

protected:
    explicit QAudioInputSelectorControl(QObject *parent /TransferThis/ = 0);
};
