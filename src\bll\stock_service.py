#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Service pour la gestion des stocks et mouvements
"""

import logging
from typing import List, Optional, Dict, Any
from datetime import datetime, timedelta
from sqlalchemy.orm import Session
from sqlalchemy.exc import SQLAlchemyError
from sqlalchemy import and_, or_, desc

from src.bll.base_service import BaseService
from src.dal.models.stock import StockMovement, MovementType, MovementReason
from src.dal.models.product import Product
from src.dal.database import db_manager

class StockService(BaseService):
    """Service pour la gestion des stocks"""
    
    def __init__(self):
        super().__init__(StockMovement)
        self.logger = logging.getLogger(__name__)
    
    def create_movement(self, product_id: int, quantity: float, movement_type: MovementType,
                       reason: MovementReason, user_id: int, reference: str = None,
                       notes: str = None, date: datetime = None, session: Optional[Session] = None) -> Optional[StockMovement]:
        """Crée un mouvement de stock et met à jour le stock du produit"""
        try:
            if session:
                return self._create_movement_in_session(
                    session, product_id, quantity, movement_type, reason, 
                    user_id, reference, notes, date
                )
            else:
                with db_manager.get_session() as session:
                    return self._create_movement_in_session(
                        session, product_id, quantity, movement_type, reason,
                        user_id, reference, notes, date
                    )
                    
        except SQLAlchemyError as e:
            self.logger.error(f"Erreur lors de la création du mouvement: {e}")
            return None
    
    def _create_movement_in_session(self, session: Session, product_id: int, quantity: float,
                                  movement_type: MovementType, reason: MovementReason,
                                  user_id: int, reference: str = None, notes: str = None,
                                  date: datetime = None) -> Optional[StockMovement]:
        """Crée un mouvement dans une session donnée"""
        # Récupérer le produit
        product = session.query(Product).filter(Product.id == product_id).first()
        if not product:
            self.logger.error(f"Produit {product_id} introuvable")
            return None
        
        if not product.track_stock:
            self.logger.warning(f"Le produit {product.name} ne suit pas le stock")
            return None
        
        # Ajuster la quantité selon le type de mouvement
        if movement_type == MovementType.OUT and quantity > 0:
            quantity = -quantity
        elif movement_type == MovementType.IN and quantity < 0:
            quantity = -quantity
        
        # Vérifier le stock disponible pour les sorties
        if quantity < 0 and (product.current_stock + quantity) < 0:
            self.logger.warning(f"Stock insuffisant pour {product.name}: {product.current_stock} disponible, {abs(quantity)} demandé")
            # Permettre quand même le mouvement mais avec avertissement
        
        # Créer le mouvement
        movement = StockMovement(
            product_id=product_id,
            movement_type=movement_type,
            quantity=quantity,
            reason=reason,
            reference=reference,
            notes=notes,
            date=date or datetime.now(),
            user_id=user_id
        )
        
        session.add(movement)
        
        # Mettre à jour le stock du produit
        product.current_stock += quantity
        if product.current_stock < 0:
            product.current_stock = 0  # Éviter les stocks négatifs
        
        session.flush()
        session.refresh(movement)
        
        self.logger.info(f"Mouvement créé: {product.name} {quantity:+.3f} (nouveau stock: {product.current_stock:.3f})")
        return movement
    
    def get_movements_by_product(self, product_id: int, limit: int = None, session: Optional[Session] = None) -> List[StockMovement]:
        """Récupère les mouvements d'un produit"""
        try:
            if session:
                query = session.query(StockMovement).filter(StockMovement.product_id == product_id)
            else:
                with db_manager.get_session() as session:
                    query = session.query(StockMovement).filter(StockMovement.product_id == product_id)
                    query = query.order_by(desc(StockMovement.date))
                    
                    if limit:
                        query = query.limit(limit)
                    
                    return query.all()
            
            # Si session fournie
            query = query.order_by(desc(StockMovement.date))
            if limit:
                query = query.limit(limit)
            
            return query.all()
            
        except SQLAlchemyError as e:
            self.logger.error(f"Erreur lors de la récupération des mouvements: {e}")
            return []
    
    def get_movements_by_date_range(self, start_date: datetime, end_date: datetime, 
                                  session: Optional[Session] = None) -> List[StockMovement]:
        """Récupère les mouvements dans une période"""
        try:
            if session:
                query = session.query(StockMovement)
            else:
                with db_manager.get_session() as session:
                    query = session.query(StockMovement)
                    query = query.filter(and_(
                        StockMovement.date >= start_date,
                        StockMovement.date <= end_date
                    ))
                    query = query.order_by(desc(StockMovement.date))
                    
                    return query.all()
            
            # Si session fournie
            query = query.filter(and_(
                StockMovement.date >= start_date,
                StockMovement.date <= end_date
            ))
            query = query.order_by(desc(StockMovement.date))
            
            return query.all()
            
        except SQLAlchemyError as e:
            self.logger.error(f"Erreur lors de la récupération des mouvements par date: {e}")
            return []
    
    def get_movements_by_type(self, movement_type: MovementType, session: Optional[Session] = None) -> List[StockMovement]:
        """Récupère les mouvements par type"""
        return self.get_all(session=session, movement_type=movement_type)
    
    def get_recent_movements(self, days: int = 7, session: Optional[Session] = None) -> List[StockMovement]:
        """Récupère les mouvements récents"""
        start_date = datetime.now() - timedelta(days=days)
        end_date = datetime.now()
        return self.get_movements_by_date_range(start_date, end_date, session)
    
    def calculate_stock_value(self, session: Optional[Session] = None) -> Dict[str, float]:
        """Calcule la valeur totale du stock"""
        try:
            if session:
                products = session.query(Product).filter(
                    and_(Product.track_stock == True, Product.current_stock > 0)
                ).all()
            else:
                with db_manager.get_session() as session:
                    products = session.query(Product).filter(
                        and_(Product.track_stock == True, Product.current_stock > 0)
                    ).all()
            
            total_purchase_value = 0.0
            total_sale_value = 0.0
            
            for product in products:
                if product.current_stock > 0:
                    if product.purchase_price:
                        total_purchase_value += product.current_stock * float(product.purchase_price)
                    if product.sale_price:
                        total_sale_value += product.current_stock * float(product.sale_price)
            
            return {
                'purchase_value': total_purchase_value,
                'sale_value': total_sale_value,
                'potential_margin': total_sale_value - total_purchase_value
            }
            
        except SQLAlchemyError as e:
            self.logger.error(f"Erreur lors du calcul de la valeur du stock: {e}")
            return {'purchase_value': 0.0, 'sale_value': 0.0, 'potential_margin': 0.0}
    
    def get_low_stock_products(self, session: Optional[Session] = None) -> List[Product]:
        """Récupère les produits en stock bas"""
        try:
            if session:
                query = session.query(Product)
            else:
                with db_manager.get_session() as session:
                    query = session.query(Product)
                    query = query.filter(and_(
                        Product.track_stock == True,
                        Product.current_stock <= Product.min_stock,
                        Product.current_stock > 0
                    ))
                    
                    return query.all()
            
            # Si session fournie
            query = query.filter(and_(
                Product.track_stock == True,
                Product.current_stock <= Product.min_stock,
                Product.current_stock > 0
            ))
            
            return query.all()
            
        except SQLAlchemyError as e:
            self.logger.error(f"Erreur lors de la récupération des produits en stock bas: {e}")
            return []
    
    def get_out_of_stock_products(self, session: Optional[Session] = None) -> List[Product]:
        """Récupère les produits en rupture de stock"""
        try:
            if session:
                query = session.query(Product)
            else:
                with db_manager.get_session() as session:
                    query = session.query(Product)
                    query = query.filter(and_(
                        Product.track_stock == True,
                        Product.current_stock <= 0
                    ))
                    
                    return query.all()
            
            # Si session fournie
            query = query.filter(and_(
                Product.track_stock == True,
                Product.current_stock <= 0
            ))
            
            return query.all()
            
        except SQLAlchemyError as e:
            self.logger.error(f"Erreur lors de la récupération des produits en rupture: {e}")
            return []
    
    def get_stock_statistics(self, session: Optional[Session] = None) -> Dict[str, Any]:
        """Récupère les statistiques de stock"""
        try:
            if session:
                products_query = session.query(Product).filter(Product.track_stock == True)
            else:
                with db_manager.get_session() as session:
                    products_query = session.query(Product).filter(Product.track_stock == True)
                    
                    total_products = products_query.count()
                    products_in_stock = products_query.filter(Product.current_stock > 0).count()
                    low_stock = products_query.filter(and_(
                        Product.current_stock <= Product.min_stock,
                        Product.current_stock > 0
                    )).count()
                    out_of_stock = products_query.filter(Product.current_stock <= 0).count()
                    
                    # Mouvements récents
                    recent_movements = self.get_recent_movements(7, session)
                    
                    # Valeur du stock
                    stock_value = self.calculate_stock_value(session)
                    
                    return {
                        'total_products': total_products,
                        'products_in_stock': products_in_stock,
                        'low_stock_count': low_stock,
                        'out_of_stock_count': out_of_stock,
                        'recent_movements_count': len(recent_movements),
                        'stock_value': stock_value
                    }
            
            # Si session fournie
            total_products = products_query.count()
            products_in_stock = products_query.filter(Product.current_stock > 0).count()
            low_stock = products_query.filter(and_(
                Product.current_stock <= Product.min_stock,
                Product.current_stock > 0
            )).count()
            out_of_stock = products_query.filter(Product.current_stock <= 0).count()
            
            # Mouvements récents
            recent_movements = self.get_recent_movements(7, session)
            
            # Valeur du stock
            stock_value = self.calculate_stock_value(session)
            
            return {
                'total_products': total_products,
                'products_in_stock': products_in_stock,
                'low_stock_count': low_stock,
                'out_of_stock_count': out_of_stock,
                'recent_movements_count': len(recent_movements),
                'stock_value': stock_value
            }
            
        except SQLAlchemyError as e:
            self.logger.error(f"Erreur lors du calcul des statistiques: {e}")
            return {}
    
    def adjust_stock(self, product_id: int, new_quantity: float, reason: MovementReason,
                    user_id: int, reference: str = None, notes: str = None,
                    session: Optional[Session] = None) -> Optional[StockMovement]:
        """Ajuste le stock d'un produit à une quantité donnée"""
        try:
            if session:
                return self._adjust_stock_in_session(
                    session, product_id, new_quantity, reason, user_id, reference, notes
                )
            else:
                with db_manager.get_session() as session:
                    return self._adjust_stock_in_session(
                        session, product_id, new_quantity, reason, user_id, reference, notes
                    )
                    
        except SQLAlchemyError as e:
            self.logger.error(f"Erreur lors de l'ajustement du stock: {e}")
            return None
    
    def _adjust_stock_in_session(self, session: Session, product_id: int, new_quantity: float,
                               reason: MovementReason, user_id: int, reference: str = None,
                               notes: str = None) -> Optional[StockMovement]:
        """Ajuste le stock dans une session donnée"""
        # Récupérer le produit
        product = session.query(Product).filter(Product.id == product_id).first()
        if not product:
            return None
        
        if not product.track_stock:
            return None
        
        # Calculer la différence
        current_stock = float(product.current_stock)
        difference = new_quantity - current_stock
        
        if difference == 0:
            self.logger.info(f"Aucun ajustement nécessaire pour {product.name}")
            return None
        
        # Créer le mouvement d'ajustement
        movement = self.create_movement(
            product_id=product_id,
            quantity=difference,
            movement_type=MovementType.ADJUSTMENT,
            reason=reason,
            user_id=user_id,
            reference=reference,
            notes=notes,
            session=session
        )
        
        return movement
    
    def validate_data(self, data: Dict[str, Any], is_update: bool = False) -> List[str]:
        """Valide les données de mouvement"""
        errors = super().validate_data(data, is_update)
        
        # Validation du produit
        product_id = data.get('product_id')
        if not product_id:
            errors.append("Le produit est obligatoire")
        
        # Validation de la quantité
        quantity = data.get('quantity', 0)
        if quantity == 0:
            errors.append("La quantité ne peut pas être zéro")
        
        # Validation de l'utilisateur
        user_id = data.get('user_id')
        if not user_id:
            errors.append("L'utilisateur est obligatoire")
        
        return errors
