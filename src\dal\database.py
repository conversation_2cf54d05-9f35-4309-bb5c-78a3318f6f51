#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Gestionnaire de base de données pour GSCOM
Configuration et gestion des connexions SQLAlchemy
"""

import logging
from sqlalchemy import create_engine, event
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker, Session
from sqlalchemy.pool import StaticPool
from contextlib import contextmanager
from typing import Generator

from src.core.config import app_config

# Base pour tous les modèles
Base = declarative_base()

class DatabaseManager:
    """Gestionnaire de base de données"""

    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.engine = None
        self.SessionLocal = None
        self._initialize_database()

    def _initialize_database(self):
        """Initialise la connexion à la base de données"""
        try:
            database_url = app_config.database_url
            self.logger.info(f"Connexion à la base de données: {database_url.split('://')[0]}://...")

            # Configuration du moteur selon le type de base
            if database_url.startswith('sqlite'):
                self.engine = create_engine(
                    database_url,
                    poolclass=StaticPool,
                    connect_args={
                        "check_same_thread": False,
                        "timeout": 20
                    },
                    echo=False  # Mettre à True pour debug SQL
                )
                # Activer les clés étrangères pour SQLite
                @event.listens_for(self.engine, "connect")
                def set_sqlite_pragma(dbapi_connection, connection_record):
                    cursor = dbapi_connection.cursor()
                    cursor.execute("PRAGMA foreign_keys=ON")
                    cursor.close()
            else:
                self.engine = create_engine(
                    database_url,
                    pool_pre_ping=True,
                    pool_recycle=300,
                    echo=False
                )

            # Créer la session factory
            self.SessionLocal = sessionmaker(
                autocommit=False,
                autoflush=False,
                bind=self.engine
            )

            self.logger.info("Base de données initialisée avec succès")

        except Exception as e:
            self.logger.error(f"Erreur lors de l'initialisation de la base de données: {e}")
            raise

    def create_tables(self):
        """Crée toutes les tables"""
        try:
            # Importer tous les modèles pour s'assurer qu'ils sont enregistrés
            import src.dal.models

            Base.metadata.create_all(bind=self.engine)
            self.logger.info("Tables créées avec succès")

        except Exception as e:
            self.logger.error(f"Erreur lors de la création des tables: {e}")
            raise

    @contextmanager
    def get_session(self) -> Generator[Session, None, None]:
        """Context manager pour les sessions de base de données"""
        session = self.SessionLocal()
        try:
            yield session
            session.commit()
        except Exception as e:
            session.rollback()
            self.logger.error(f"Erreur de session de base de données: {e}")
            raise
        finally:
            session.close()

    def get_session_direct(self) -> Session:
        """Retourne une session directe (à fermer manuellement)"""
        return self.SessionLocal()

    def test_connection(self) -> bool:
        """Test la connexion à la base de données"""
        try:
            from sqlalchemy import text
            with self.get_session() as session:
                session.execute(text("SELECT 1"))
            return True
        except Exception as e:
            self.logger.error(f"Test de connexion échoué: {e}")
            return False

    def backup_database(self, backup_path: str) -> bool:
        """Sauvegarde la base de données"""
        try:
            if app_config.get('database.type') == 'sqlite':
                import shutil
                sqlite_path = app_config.get('database.sqlite_path')
                shutil.copy2(sqlite_path, backup_path)
                self.logger.info(f"Sauvegarde SQLite créée: {backup_path}")
                return True
            else:
                # Pour PostgreSQL, utiliser pg_dump
                self.logger.warning("Sauvegarde PostgreSQL non implémentée")
                return False

        except Exception as e:
            self.logger.error(f"Erreur lors de la sauvegarde: {e}")
            return False

    def restore_database(self, backup_path: str) -> bool:
        """Restaure la base de données"""
        try:
            if app_config.get('database.type') == 'sqlite':
                import shutil
                sqlite_path = app_config.get('database.sqlite_path')
                shutil.copy2(backup_path, sqlite_path)
                self.logger.info(f"Base de données restaurée depuis: {backup_path}")
                return True
            else:
                self.logger.warning("Restauration PostgreSQL non implémentée")
                return False

        except Exception as e:
            self.logger.error(f"Erreur lors de la restauration: {e}")
            return False

# Instance globale du gestionnaire de base de données
db_manager = DatabaseManager()
