// qtesttouch.sip generated by MetaSIP
//
// This file is part of the QtTest Python extension module.
//
// Copyright (c) 2024 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt5.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


namespace QTest
{
%TypeHeaderCode
#include <qtesttouch.h>
%End

    class QTouchEventSequence
    {
%TypeHeaderCode
#include <qtesttouch.h>
%End

    public:
        ~QTouchEventSequence();
        QTest::QTouchEventSequence &press(int touchId, const QPoint &pt, QWindow *window = 0);
        QTest::QTouchEventSequence &move(int touchId, const QPoint &pt, QWindow *window = 0);
        QTest::QTouchEventSequence &release(int touchId, const QPoint &pt, QWindow *window = 0);
        QTest::QTouchEventSequence &stationary(int touchId);
        QTest::QTouchEventSequence &press(int touchId, const QPoint &pt, QWidget *widget) [QTest::QTouchEventSequence & (int touchId, const QPoint &pt, QWidget *widget = 0)];
        QTest::QTouchEventSequence &move(int touchId, const QPoint &pt, QWidget *widget) [QTest::QTouchEventSequence & (int touchId, const QPoint &pt, QWidget *widget = 0)];
        QTest::QTouchEventSequence &release(int touchId, const QPoint &pt, QWidget *widget) [QTest::QTouchEventSequence & (int touchId, const QPoint &pt, QWidget *widget = 0)];
        void commit(bool processEvents = true) /ReleaseGIL/;

    private:
        QTouchEventSequence(QWidget *widget, QTouchDevice *aDevice, bool autoCommit);
        QTouchEventSequence(QWindow *window, QTouchDevice *aDevice, bool autoCommit);
    };

    QTest::QTouchEventSequence touchEvent(QWidget *widget, QTouchDevice *device);
%MethodCode
        // Disable auto-committing so that we can copy the instance around.
        sipRes = new QTest::QTouchEventSequence(QTest::touchEvent(a0, a1, false));
%End

    QTest::QTouchEventSequence touchEvent(QWindow *window, QTouchDevice *device);
%MethodCode
        // Disable auto-committing so that we can copy the instance around.
        sipRes = new QTest::QTouchEventSequence(QTest::touchEvent(a0, a1, false));
%End
};
