// qactiongroup.sip generated by MetaSIP
//
// This file is part of the QtWidgets Python extension module.
//
// Copyright (c) 2024 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt5.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file L<PERSON>ENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


class QActionGroup : public QObject
{
%TypeHeaderCode
#include <qactiongroup.h>
%End

public:
    explicit QActionGroup(QObject *parent /TransferThis/);
    virtual ~QActionGroup();
    QAction *addAction(QAction *a /Transfer/);
    QAction *addAction(const QString &text) /Transfer/;
    QAction *addAction(const QIcon &icon, const QString &text) /Transfer/;
    void removeAction(QAction *a /TransferBack/);
    QList<QAction *> actions() const;
    QAction *checkedAction() const;
    bool isExclusive() const;
    bool isEnabled() const;
    bool isVisible() const;

public slots:
    void setEnabled(bool);
    void setDisabled(bool b);
    void setVisible(bool);
    void setExclusive(bool);

signals:
    void triggered(QAction *);
    void hovered(QAction *);

public:
%If (Qt_5_14_0 -)

    enum class ExclusionPolicy
    {
        None /PyName=None_/,
        Exclusive,
        ExclusiveOptional,
    };

%End
%If (Qt_5_14_0 -)
    QActionGroup::ExclusionPolicy exclusionPolicy() const;
%End

public slots:
%If (Qt_5_14_0 -)
    void setExclusionPolicy(QActionGroup::ExclusionPolicy policy);
%End

private:
    QActionGroup(const QActionGroup &);
};
