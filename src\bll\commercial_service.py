#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Service pour la gestion commerciale (devis, commandes, factures)
"""

import logging
from typing import List, Optional, Dict, Any
from datetime import datetime, timedelta
from sqlalchemy.orm import Session
from sqlalchemy.exc import SQLAlchemyError
from sqlalchemy import and_, or_, desc, func
from decimal import Decimal

from src.bll.base_service import BaseService
from src.dal.models.commercial import (
    Quote, Order, Delivery, Invoice, Payment,
    QuoteLine, OrderLine, DeliveryLine, InvoiceLine,
    DocumentStatus, InvoiceStatus, PaymentStatus, PaymentMethod
)
from src.dal.models.product import Product
from src.dal.models.client import Client
from src.dal.database import db_manager

class CommercialService(BaseService):
    """Service pour la gestion commerciale"""
    
    def __init__(self):
        super().__init__(Quote)  # Modèle de base
        self.logger = logging.getLogger(__name__)
    
    # === GESTION DES DEVIS ===
    
    def create_quote(self, client_id: int, lines_data: List[Dict], 
                    notes: str = None, terms: str = None, 
                    valid_days: int = 30, session: Optional[Session] = None) -> Optional[Quote]:
        """Crée un nouveau devis"""
        try:
            if session:
                return self._create_quote_in_session(session, client_id, lines_data, notes, terms, valid_days)
            else:
                with db_manager.get_session() as session:
                    return self._create_quote_in_session(session, client_id, lines_data, notes, terms, valid_days)
                    
        except SQLAlchemyError as e:
            self.logger.error(f"Erreur lors de la création du devis: {e}")
            return None
    
    def _create_quote_in_session(self, session: Session, client_id: int, lines_data: List[Dict],
                               notes: str = None, terms: str = None, valid_days: int = 30) -> Optional[Quote]:
        """Crée un devis dans une session donnée"""
        # Générer le numéro de devis
        quote_number = self._generate_quote_number(session)
        
        # Créer le devis
        quote = Quote(
            number=quote_number,
            client_id=client_id,
            notes=notes,
            terms=terms,
            valid_until=datetime.now() + timedelta(days=valid_days)
        )
        
        session.add(quote)
        session.flush()  # Pour obtenir l'ID
        
        # Ajouter les lignes
        total_subtotal = Decimal('0')
        total_tax = Decimal('0')
        
        for line_data in lines_data:
            line = QuoteLine(
                quote_id=quote.id,
                product_id=line_data['product_id'],
                description=line_data.get('description', ''),
                quantity=Decimal(str(line_data['quantity'])),
                unit_price=Decimal(str(line_data['unit_price'])),
                discount_rate=Decimal(str(line_data.get('discount_rate', 0))),
                tax_rate=Decimal(str(line_data.get('tax_rate', 19)))
            )
            
            # Calculer le total de la ligne
            line_subtotal = line.quantity * line.unit_price
            line_discount = line_subtotal * (line.discount_rate / 100)
            line_taxable = line_subtotal - line_discount
            line_tax = line_taxable * (line.tax_rate / 100)
            line.total = line_taxable + line_tax
            
            total_subtotal += line_subtotal
            total_tax += line_tax
            
            session.add(line)
        
        # Mettre à jour les totaux du devis
        quote.subtotal = total_subtotal
        quote.tax_amount = total_tax
        quote.total = total_subtotal + total_tax
        
        session.commit()
        session.refresh(quote)
        
        self.logger.info(f"Devis créé: {quote.number}")
        return quote
    
    def convert_quote_to_order(self, quote_id: int, session: Optional[Session] = None) -> Optional[Order]:
        """Convertit un devis en commande"""
        try:
            if session:
                return self._convert_quote_to_order_in_session(session, quote_id)
            else:
                with db_manager.get_session() as session:
                    return self._convert_quote_to_order_in_session(session, quote_id)
                    
        except SQLAlchemyError as e:
            self.logger.error(f"Erreur lors de la conversion devis->commande: {e}")
            return None
    
    def _convert_quote_to_order_in_session(self, session: Session, quote_id: int) -> Optional[Order]:
        """Convertit un devis en commande dans une session donnée"""
        # Récupérer le devis
        quote = session.query(Quote).filter(Quote.id == quote_id).first()
        if not quote:
            return None
        
        # Générer le numéro de commande
        order_number = self._generate_order_number(session)
        
        # Créer la commande
        order = Order(
            number=order_number,
            client_id=quote.client_id,
            quote_id=quote.id,
            subtotal=quote.subtotal,
            tax_amount=quote.tax_amount,
            total=quote.total,
            notes=quote.notes
        )
        
        session.add(order)
        session.flush()
        
        # Copier les lignes
        for quote_line in quote.lines:
            order_line = OrderLine(
                order_id=order.id,
                product_id=quote_line.product_id,
                description=quote_line.description,
                quantity=quote_line.quantity,
                unit_price=quote_line.unit_price,
                discount_rate=quote_line.discount_rate,
                tax_rate=quote_line.tax_rate,
                total=quote_line.total
            )
            session.add(order_line)
        
        # Marquer le devis comme confirmé
        quote.status = DocumentStatus.CONFIRMED
        
        session.commit()
        session.refresh(order)
        
        self.logger.info(f"Devis {quote.number} converti en commande {order.number}")
        return order
    
    # === GESTION DES COMMANDES ===
    
    def create_delivery_from_order(self, order_id: int, delivery_address: str = None,
                                 session: Optional[Session] = None) -> Optional[Delivery]:
        """Crée un bon de livraison à partir d'une commande"""
        try:
            if session:
                return self._create_delivery_from_order_in_session(session, order_id, delivery_address)
            else:
                with db_manager.get_session() as session:
                    return self._create_delivery_from_order_in_session(session, order_id, delivery_address)
                    
        except SQLAlchemyError as e:
            self.logger.error(f"Erreur lors de la création du bon de livraison: {e}")
            return None
    
    def _create_delivery_from_order_in_session(self, session: Session, order_id: int,
                                             delivery_address: str = None) -> Optional[Delivery]:
        """Crée un bon de livraison dans une session donnée"""
        # Récupérer la commande
        order = session.query(Order).filter(Order.id == order_id).first()
        if not order:
            return None
        
        # Générer le numéro de livraison
        delivery_number = self._generate_delivery_number(session)
        
        # Créer le bon de livraison
        delivery = Delivery(
            number=delivery_number,
            order_id=order.id,
            delivery_address=delivery_address,
            delivery_date=datetime.now()
        )
        
        session.add(delivery)
        session.flush()
        
        # Copier les lignes de commande
        for order_line in order.lines:
            delivery_line = DeliveryLine(
                delivery_id=delivery.id,
                product_id=order_line.product_id,
                description=order_line.description,
                quantity=order_line.quantity
            )
            session.add(delivery_line)
        
        session.commit()
        session.refresh(delivery)
        
        self.logger.info(f"Bon de livraison créé: {delivery.number}")
        return delivery
    
    # === GESTION DES FACTURES ===
    
    def create_invoice_from_order(self, order_id: int, due_days: int = 30,
                                session: Optional[Session] = None) -> Optional[Invoice]:
        """Crée une facture à partir d'une commande"""
        try:
            if session:
                return self._create_invoice_from_order_in_session(session, order_id, due_days)
            else:
                with db_manager.get_session() as session:
                    return self._create_invoice_from_order_in_session(session, order_id, due_days)
                    
        except SQLAlchemyError as e:
            self.logger.error(f"Erreur lors de la création de la facture: {e}")
            return None
    
    def _create_invoice_from_order_in_session(self, session: Session, order_id: int,
                                            due_days: int = 30) -> Optional[Invoice]:
        """Crée une facture dans une session donnée"""
        # Récupérer la commande
        order = session.query(Order).filter(Order.id == order_id).first()
        if not order:
            return None
        
        # Générer le numéro de facture
        invoice_number = self._generate_invoice_number(session)
        
        # Créer la facture
        invoice = Invoice(
            number=invoice_number,
            client_id=order.client_id,
            order_id=order.id,
            due_date=datetime.now() + timedelta(days=due_days),
            subtotal=order.subtotal,
            tax_amount=order.tax_amount,
            total=order.total,
            notes=order.notes
        )
        
        session.add(invoice)
        session.flush()
        
        # Copier les lignes de commande
        for order_line in order.lines:
            invoice_line = InvoiceLine(
                invoice_id=invoice.id,
                product_id=order_line.product_id,
                description=order_line.description,
                quantity=order_line.quantity,
                unit_price=order_line.unit_price,
                discount_rate=order_line.discount_rate,
                tax_rate=order_line.tax_rate
            )
            invoice_line.calculate_amounts()
            session.add(invoice_line)
        
        session.commit()
        session.refresh(invoice)
        
        self.logger.info(f"Facture créée: {invoice.number}")
        return invoice
    
    # === GESTION DES PAIEMENTS ===
    
    def create_payment(self, invoice_id: int, amount: float, method: PaymentMethod,
                      reference: str = None, notes: str = None,
                      session: Optional[Session] = None) -> Optional[Payment]:
        """Crée un paiement pour une facture"""
        try:
            if session:
                return self._create_payment_in_session(session, invoice_id, amount, method, reference, notes)
            else:
                with db_manager.get_session() as session:
                    return self._create_payment_in_session(session, invoice_id, amount, method, reference, notes)
                    
        except SQLAlchemyError as e:
            self.logger.error(f"Erreur lors de la création du paiement: {e}")
            return None
    
    def _create_payment_in_session(self, session: Session, invoice_id: int, amount: float,
                                 method: PaymentMethod, reference: str = None,
                                 notes: str = None) -> Optional[Payment]:
        """Crée un paiement dans une session donnée"""
        # Récupérer la facture
        invoice = session.query(Invoice).filter(Invoice.id == invoice_id).first()
        if not invoice:
            return None
        
        # Générer le numéro de paiement
        payment_number = self._generate_payment_number(session)
        
        # Créer le paiement
        payment = Payment(
            number=payment_number,
            client_id=invoice.client_id,
            invoice_id=invoice.id,
            amount=Decimal(str(amount)),
            method=method,
            reference=reference,
            notes=notes,
            status=PaymentStatus.COMPLETED
        )
        
        session.add(payment)
        
        # Mettre à jour le montant payé de la facture
        invoice.paid_amount += Decimal(str(amount))
        invoice.update_status()
        
        session.commit()
        session.refresh(payment)
        
        self.logger.info(f"Paiement créé: {payment.number} - {amount} DA")
        return payment
    
    # === MÉTHODES UTILITAIRES ===
    
    def _generate_quote_number(self, session: Session) -> str:
        """Génère un numéro de devis"""
        year = datetime.now().year
        count = session.query(Quote).filter(
            func.extract('year', Quote.created_at) == year
        ).count()
        return f"DEV{year}{count + 1:04d}"
    
    def _generate_order_number(self, session: Session) -> str:
        """Génère un numéro de commande"""
        year = datetime.now().year
        count = session.query(Order).filter(
            func.extract('year', Order.created_at) == year
        ).count()
        return f"CMD{year}{count + 1:04d}"
    
    def _generate_delivery_number(self, session: Session) -> str:
        """Génère un numéro de bon de livraison"""
        year = datetime.now().year
        count = session.query(Delivery).filter(
            func.extract('year', Delivery.created_at) == year
        ).count()
        return f"BL{year}{count + 1:04d}"
    
    def _generate_invoice_number(self, session: Session) -> str:
        """Génère un numéro de facture"""
        year = datetime.now().year
        count = session.query(Invoice).filter(
            func.extract('year', Invoice.created_at) == year
        ).count()
        return f"FAC{year}{count + 1:04d}"
    
    def _generate_payment_number(self, session: Session) -> str:
        """Génère un numéro de paiement"""
        year = datetime.now().year
        count = session.query(Payment).filter(
            func.extract('year', Payment.created_at) == year
        ).count()
        return f"PAY{year}{count + 1:04d}"
    
    # === STATISTIQUES ===
    
    def get_commercial_statistics(self, session: Optional[Session] = None) -> Dict[str, Any]:
        """Récupère les statistiques commerciales"""
        try:
            if session:
                return self._get_commercial_statistics_in_session(session)
            else:
                with db_manager.get_session() as session:
                    return self._get_commercial_statistics_in_session(session)
                    
        except SQLAlchemyError as e:
            self.logger.error(f"Erreur lors du calcul des statistiques: {e}")
            return {}
    
    def _get_commercial_statistics_in_session(self, session: Session) -> Dict[str, Any]:
        """Calcule les statistiques dans une session donnée"""
        # Compter les documents
        quotes_count = session.query(Quote).count()
        orders_count = session.query(Order).count()
        invoices_count = session.query(Invoice).count()
        
        # Montants
        total_quotes = session.query(func.sum(Quote.total)).scalar() or 0
        total_orders = session.query(func.sum(Order.total)).scalar() or 0
        total_invoices = session.query(func.sum(Invoice.total)).scalar() or 0
        total_paid = session.query(func.sum(Invoice.paid_amount)).scalar() or 0
        
        # Factures en retard
        overdue_invoices = session.query(Invoice).filter(
            and_(
                Invoice.due_date < datetime.now(),
                Invoice.status.in_([InvoiceStatus.SENT, InvoiceStatus.PARTIAL])
            )
        ).count()
        
        return {
            'quotes_count': quotes_count,
            'orders_count': orders_count,
            'invoices_count': invoices_count,
            'total_quotes': float(total_quotes),
            'total_orders': float(total_orders),
            'total_invoices': float(total_invoices),
            'total_paid': float(total_paid),
            'outstanding_amount': float(total_invoices) - float(total_paid),
            'overdue_invoices': overdue_invoices
        }
    
    def get_overdue_invoices(self, session: Optional[Session] = None) -> List[Invoice]:
        """Récupère les factures en retard"""
        try:
            if session:
                query = session.query(Invoice)
            else:
                with db_manager.get_session() as session:
                    query = session.query(Invoice)
                    query = query.filter(and_(
                        Invoice.due_date < datetime.now(),
                        Invoice.status.in_([InvoiceStatus.SENT, InvoiceStatus.PARTIAL])
                    ))
                    return query.all()
            
            # Si session fournie
            query = query.filter(and_(
                Invoice.due_date < datetime.now(),
                Invoice.status.in_([InvoiceStatus.SENT, InvoiceStatus.PARTIAL])
            ))
            return query.all()
            
        except SQLAlchemyError as e:
            self.logger.error(f"Erreur lors de la récupération des factures en retard: {e}")
            return []
