#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Fenêtre de connexion moderne pour GSCOM
Design futuriste avec effets glassmorphism et animations
"""

import sys
import logging
from PyQt5.QtWidgets import *
from PyQt5.QtCore import *
from PyQt5.QtGui import *

from src.bll.user_service import UserService
from src.core.config import app_config

class LoginWindow(QMainWindow):
    """Fenêtre de connexion moderne avec design futuriste"""

    # Signal émis lors d'une connexion réussie
    login_successful = pyqtSignal(object)  # Émet l'objet utilisateur

    def __init__(self):
        super().__init__()
        self.user_service = UserService()
        self.logger = logging.getLogger(__name__)
        self.current_user = None

        self.init_ui()
        self.setup_animations()
        self.apply_styles()

    def init_ui(self):
        """Initialise l'interface utilisateur"""
        # Configuration de la fenêtre
        self.setWindowTitle("GSCOM - Connexion")
        self.setFixedSize(400, 500)
        self.setWindowFlags(Qt.FramelessWindowHint)
        self.setAttribute(Qt.WA_TranslucentBackground)

        # Centrer la fenêtre
        self.center_window()

        # Widget principal
        self.central_widget = QWidget()
        self.setCentralWidget(self.central_widget)

        # Layout principal
        main_layout = QVBoxLayout(self.central_widget)
        main_layout.setContentsMargins(0, 0, 0, 0)

        # Container principal avec effet glassmorphism
        self.main_container = QFrame()
        self.main_container.setObjectName("mainContainer")
        main_layout.addWidget(self.main_container)

        # Layout du container
        container_layout = QVBoxLayout(self.main_container)
        container_layout.setContentsMargins(40, 40, 40, 40)
        container_layout.setSpacing(25)

        # Header avec logo et titre
        self.create_header(container_layout)

        # Formulaire de connexion
        self.create_login_form(container_layout)

        # Boutons d'action
        self.create_action_buttons(container_layout)

        # Footer
        self.create_footer(container_layout)

        # Barre de titre personnalisée
        self.create_title_bar()

    def create_header(self, layout):
        """Crée l'en-tête avec logo et titre"""
        header_layout = QVBoxLayout()
        header_layout.setSpacing(15)

        # Logo (placeholder)
        logo_label = QLabel()
        logo_label.setAlignment(Qt.AlignCenter)
        logo_label.setFixedSize(80, 80)
        logo_label.setStyleSheet("""
            QLabel {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #00d4ff, stop:1 #ff00ff);
                border-radius: 40px;
                border: 2px solid rgba(255, 255, 255, 0.3);
            }
        """)
        header_layout.addWidget(logo_label)

        # Titre principal
        title_label = QLabel("GSCOM")
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setObjectName("titleLabel")
        header_layout.addWidget(title_label)

        # Sous-titre
        subtitle_label = QLabel("Gestion Commerciale & Stock")
        subtitle_label.setAlignment(Qt.AlignCenter)
        subtitle_label.setObjectName("subtitleLabel")
        header_layout.addWidget(subtitle_label)

        layout.addLayout(header_layout)

    def create_login_form(self, layout):
        """Crée le formulaire de connexion"""
        form_layout = QVBoxLayout()
        form_layout.setSpacing(20)

        # Champ nom d'utilisateur
        self.username_container = self.create_input_field(
            "Nom d'utilisateur ou Email",
            "👤",
            False
        )
        self.username_input = self.username_container.findChild(QLineEdit)
        form_layout.addWidget(self.username_container)

        # Champ mot de passe
        self.password_container = self.create_input_field(
            "Mot de passe",
            "🔒",
            True
        )
        self.password_input = self.password_container.findChild(QLineEdit)
        form_layout.addWidget(self.password_container)

        # Options
        options_layout = QHBoxLayout()

        # Case "Se souvenir de moi"
        self.remember_checkbox = QCheckBox("Se souvenir de moi")
        self.remember_checkbox.setObjectName("rememberCheckbox")
        options_layout.addWidget(self.remember_checkbox)

        options_layout.addStretch()

        # Lien "Mot de passe oublié"
        self.forgot_password_label = QLabel('<a href="#" style="color: #00d4ff;">Mot de passe oublié ?</a>')
        self.forgot_password_label.setObjectName("forgotPasswordLabel")
        self.forgot_password_label.linkActivated.connect(self.show_forgot_password)
        options_layout.addWidget(self.forgot_password_label)

        form_layout.addLayout(options_layout)

        layout.addLayout(form_layout)

    def create_input_field(self, placeholder, icon, is_password=False):
        """Crée un champ de saisie avec icône et effets"""
        container = QFrame()
        container.setObjectName("inputContainer")

        layout = QHBoxLayout(container)
        layout.setContentsMargins(15, 12, 15, 12)
        layout.setSpacing(10)

        # Icône
        icon_label = QLabel(icon)
        icon_label.setObjectName("inputIcon")
        layout.addWidget(icon_label)

        # Champ de saisie
        input_field = QLineEdit()
        input_field.setPlaceholderText(placeholder)
        input_field.setObjectName("inputField")

        if is_password:
            input_field.setEchoMode(QLineEdit.Password)

            # Bouton pour afficher/masquer le mot de passe
            toggle_button = QPushButton("👁")
            toggle_button.setObjectName("togglePasswordButton")
            toggle_button.setFixedSize(30, 30)
            toggle_button.clicked.connect(lambda: self.toggle_password_visibility(input_field, toggle_button))
            layout.addWidget(input_field)
            layout.addWidget(toggle_button)
        else:
            layout.addWidget(input_field)

        return container

    def create_action_buttons(self, layout):
        """Crée les boutons d'action"""
        buttons_layout = QVBoxLayout()
        buttons_layout.setSpacing(15)

        # Bouton de connexion
        self.login_button = QPushButton("Se connecter")
        self.login_button.setObjectName("loginButton")
        self.login_button.setFixedHeight(45)
        self.login_button.clicked.connect(self.handle_login)
        buttons_layout.addWidget(self.login_button)

        # Message d'erreur
        self.error_label = QLabel()
        self.error_label.setObjectName("errorLabel")
        self.error_label.setAlignment(Qt.AlignCenter)
        self.error_label.setWordWrap(True)
        self.error_label.hide()
        buttons_layout.addWidget(self.error_label)

        layout.addLayout(buttons_layout)

    def create_footer(self, layout):
        """Crée le pied de page"""
        footer_layout = QVBoxLayout()
        footer_layout.setSpacing(10)

        # Version
        version_label = QLabel("Version 1.0.0")
        version_label.setAlignment(Qt.AlignCenter)
        version_label.setObjectName("versionLabel")
        footer_layout.addWidget(version_label)

        # Copyright
        copyright_label = QLabel("© 2024 GSCOM Solutions")
        copyright_label.setAlignment(Qt.AlignCenter)
        copyright_label.setObjectName("copyrightLabel")
        footer_layout.addWidget(copyright_label)

        layout.addLayout(footer_layout)

    def create_title_bar(self):
        """Crée une barre de titre personnalisée"""
        self.title_bar = QFrame(self)
        self.title_bar.setFixedHeight(30)
        self.title_bar.setObjectName("titleBar")

        title_layout = QHBoxLayout(self.title_bar)
        title_layout.setContentsMargins(10, 0, 10, 0)

        # Titre
        title_label = QLabel("GSCOM")
        title_label.setObjectName("titleBarLabel")
        title_layout.addWidget(title_label)

        title_layout.addStretch()

        # Boutons de contrôle
        minimize_button = QPushButton("−")
        minimize_button.setObjectName("titleBarButton")
        minimize_button.setFixedSize(30, 30)
        minimize_button.clicked.connect(self.showMinimized)
        title_layout.addWidget(minimize_button)

        close_button = QPushButton("×")
        close_button.setObjectName("titleBarCloseButton")
        close_button.setFixedSize(30, 30)
        close_button.clicked.connect(self.close)
        title_layout.addWidget(close_button)

        # Permettre le déplacement de la fenêtre
        self.title_bar.mousePressEvent = self.mouse_press_event
        self.title_bar.mouseMoveEvent = self.mouse_move_event

    def setup_animations(self):
        """Configure les animations"""
        # Animation d'apparition
        self.fade_in_animation = QPropertyAnimation(self, b"windowOpacity")
        self.fade_in_animation.setDuration(500)
        self.fade_in_animation.setStartValue(0.0)
        self.fade_in_animation.setEndValue(1.0)
        self.fade_in_animation.setEasingCurve(QEasingCurve.OutCubic)

        # Animation de pulsation pour le bouton de connexion
        self.pulse_animation = QPropertyAnimation(self.login_button, b"geometry")
        self.pulse_animation.setDuration(200)
        self.pulse_animation.setEasingCurve(QEasingCurve.OutCubic)

    def apply_styles(self):
        """Applique les styles CSS"""
        style = """
        QMainWindow {
            background: transparent;
        }

        #mainContainer {
            background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                stop:0 rgba(10, 10, 30, 0.9),
                stop:0.5 rgba(20, 20, 50, 0.8),
                stop:1 rgba(10, 10, 30, 0.9));
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 20px;
            backdrop-filter: blur(20px);
        }

        #titleLabel {
            font-family: 'Orbitron', 'Arial Black', sans-serif;
            font-size: 32px;
            font-weight: bold;
            color: #00d4ff;
            text-shadow: 0 0 20px rgba(0, 212, 255, 0.5);
        }

        #subtitleLabel {
            font-family: 'Segoe UI', sans-serif;
            font-size: 14px;
            color: rgba(255, 255, 255, 0.8);
            margin-bottom: 10px;
        }

        #inputContainer {
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 12px;
            backdrop-filter: blur(10px);
        }

        #inputContainer:hover {
            border: 1px solid rgba(0, 212, 255, 0.5);
            background: rgba(255, 255, 255, 0.15);
        }

        #inputIcon {
            font-size: 16px;
            color: #00d4ff;
        }

        #inputField {
            background: transparent;
            border: none;
            color: white;
            font-size: 14px;
            font-family: 'Segoe UI', sans-serif;
        }

        #inputField::placeholder {
            color: rgba(255, 255, 255, 0.6);
        }

        #togglePasswordButton {
            background: transparent;
            border: none;
            color: rgba(255, 255, 255, 0.6);
            border-radius: 15px;
        }

        #togglePasswordButton:hover {
            background: rgba(255, 255, 255, 0.1);
            color: #00d4ff;
        }

        #rememberCheckbox {
            color: rgba(255, 255, 255, 0.8);
            font-size: 12px;
        }

        #rememberCheckbox::indicator {
            width: 16px;
            height: 16px;
            border: 1px solid rgba(255, 255, 255, 0.3);
            border-radius: 3px;
            background: rgba(255, 255, 255, 0.1);
        }

        #rememberCheckbox::indicator:checked {
            background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                stop:0 #00d4ff, stop:1 #ff00ff);
            border: 1px solid #00d4ff;
        }

        #forgotPasswordLabel {
            font-size: 12px;
        }

        #loginButton {
            background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                stop:0 #00d4ff, stop:1 #ff00ff);
            border: none;
            border-radius: 22px;
            color: white;
            font-size: 16px;
            font-weight: bold;
            font-family: 'Segoe UI', sans-serif;
        }

        #loginButton:hover {
            background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                stop:0 #00b8e6, stop:1 #e600e6);
            box-shadow: 0 0 20px rgba(0, 212, 255, 0.5);
        }

        #loginButton:pressed {
            background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                stop:0 #0099cc, stop:1 #cc00cc);
        }

        #errorLabel {
            color: #ff4444;
            font-size: 12px;
            background: rgba(255, 68, 68, 0.1);
            border: 1px solid rgba(255, 68, 68, 0.3);
            border-radius: 8px;
            padding: 8px;
        }

        #versionLabel, #copyrightLabel {
            color: rgba(255, 255, 255, 0.5);
            font-size: 11px;
        }

        #titleBar {
            background: rgba(0, 0, 0, 0.3);
            border-top-left-radius: 20px;
            border-top-right-radius: 20px;
        }

        #titleBarLabel {
            color: #00d4ff;
            font-weight: bold;
            font-size: 12px;
        }

        #titleBarButton {
            background: transparent;
            border: none;
            color: rgba(255, 255, 255, 0.7);
            border-radius: 15px;
            font-size: 16px;
        }

        #titleBarButton:hover {
            background: rgba(255, 255, 255, 0.1);
            color: white;
        }

        #titleBarCloseButton {
            background: transparent;
            border: none;
            color: rgba(255, 255, 255, 0.7);
            border-radius: 15px;
            font-size: 18px;
        }

        #titleBarCloseButton:hover {
            background: #ff4444;
            color: white;
        }
        """

        self.setStyleSheet(style)

    def center_window(self):
        """Centre la fenêtre sur l'écran"""
        screen = QApplication.desktop().screenGeometry()
        size = self.geometry()
        self.move(
            (screen.width() - size.width()) // 2,
            (screen.height() - size.height()) // 2
        )

    def showEvent(self, event):
        """Événement d'affichage de la fenêtre"""
        super().showEvent(event)
        self.fade_in_animation.start()

    def toggle_password_visibility(self, input_field, button):
        """Bascule la visibilité du mot de passe"""
        if input_field.echoMode() == QLineEdit.Password:
            input_field.setEchoMode(QLineEdit.Normal)
            button.setText("🙈")
        else:
            input_field.setEchoMode(QLineEdit.Password)
            button.setText("👁")

    def handle_login(self):
        """Gère la tentative de connexion"""
        username = self.username_input.text().strip()
        password = self.password_input.text()

        # Validation des champs
        if not username:
            self.show_error("Veuillez saisir votre nom d'utilisateur ou email")
            return

        if not password:
            self.show_error("Veuillez saisir votre mot de passe")
            return

        # Désactiver le bouton pendant la connexion
        self.login_button.setEnabled(False)
        self.login_button.setText("Connexion en cours...")

        # Tentative d'authentification
        user = self.user_service.authenticate(username, password)

        if user:
            self.current_user = user
            self.logger.info(f"Connexion réussie pour {user.username}")

            # Sauvegarder les préférences si demandé
            if self.remember_checkbox.isChecked():
                app_config.set('login.remember_username', username)

            # Émettre le signal de connexion réussie
            self.login_successful.emit(user)

            # Fermer la fenêtre de connexion
            self.close()

            # Ouvrir la fenêtre principale
            self.open_main_window()
        else:
            self.show_error("Nom d'utilisateur ou mot de passe incorrect")
            self.login_button.setEnabled(True)
            self.login_button.setText("Se connecter")

    def show_error(self, message):
        """Affiche un message d'erreur"""
        self.error_label.setText(message)
        self.error_label.show()

        # Animation de secousse
        self.shake_animation = QPropertyAnimation(self.main_container, b"geometry")
        self.shake_animation.setDuration(500)

        original_geometry = self.main_container.geometry()
        self.shake_animation.setKeyValueAt(0, original_geometry)
        self.shake_animation.setKeyValueAt(0.1, original_geometry.translated(5, 0))
        self.shake_animation.setKeyValueAt(0.2, original_geometry.translated(-5, 0))
        self.shake_animation.setKeyValueAt(0.3, original_geometry.translated(5, 0))
        self.shake_animation.setKeyValueAt(0.4, original_geometry.translated(-5, 0))
        self.shake_animation.setKeyValueAt(0.5, original_geometry)
        self.shake_animation.setKeyValueAt(1, original_geometry)

        self.shake_animation.start()

    def show_forgot_password(self):
        """Affiche la boîte de dialogue de récupération de mot de passe"""
        QMessageBox.information(
            self,
            "Mot de passe oublié",
            "Contactez votre administrateur système pour réinitialiser votre mot de passe."
        )

    def open_main_window(self):
        """Ouvre la fenêtre principale"""
        try:
            from src.ui.main_window import MainWindow
            self.main_window = MainWindow(self.current_user)
            self.main_window.show()
        except ImportError:
            QMessageBox.warning(
                self,
                "Erreur",
                "La fenêtre principale n'est pas encore implémentée."
            )

    def mouse_press_event(self, event):
        """Événement de pression de souris pour déplacer la fenêtre"""
        if event.button() == Qt.LeftButton:
            self.drag_position = event.globalPos() - self.frameGeometry().topLeft()
            event.accept()

    def mouse_move_event(self, event):
        """Événement de déplacement de souris"""
        if event.buttons() == Qt.LeftButton and hasattr(self, 'drag_position'):
            self.move(event.globalPos() - self.drag_position)
            event.accept()

    def keyPressEvent(self, event):
        """Gestion des touches clavier"""
        if event.key() == Qt.Key_Return or event.key() == Qt.Key_Enter:
            self.handle_login()
        elif event.key() == Qt.Key_Escape:
            self.close()
        else:
            super().keyPressEvent(event)
