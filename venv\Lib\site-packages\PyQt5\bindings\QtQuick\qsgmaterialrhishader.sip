// qsgmaterialrhishader.sip generated by MetaSIP
//
// This file is part of the QtQuick Python extension module.
//
// Copyright (c) 2024 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt5.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


%If (Qt_5_14_0 -)

class QSGMaterialRhiShader : public QSGMaterialShader
{
%TypeHeaderCode
#include <qsgmaterialrhishader.h>
%End

public:
    class RenderState
    {
%TypeHeaderCode
#include <qsgmaterialrhishader.h>
%End

        typedef QSGMaterialShader::RenderState::DirtyStates DirtyStates;

    public:
        QSGMaterialRhiShader::RenderState::DirtyStates dirtyStates() const;
        bool isMatrixDirty() const;
        bool isOpacityDirty() const;
        float opacity() const;
        QMatrix4x4 combinedMatrix() const;
        QMatrix4x4 modelViewMatrix() const;
        QMatrix4x4 projectionMatrix() const;
        QRect viewportRect() const;
        QRect deviceRect() const;
        float determinant() const;
        float devicePixelRatio() const;
        QByteArray *uniformData();
    };

    struct GraphicsPipelineState
    {
%TypeHeaderCode
#include <qsgmaterialrhishader.h>
%End

        enum BlendFactor
        {
            Zero,
            One,
            SrcColor,
            OneMinusSrcColor,
            DstColor,
            OneMinusDstColor,
            SrcAlpha,
            OneMinusSrcAlpha,
            DstAlpha,
            OneMinusDstAlpha,
            ConstantColor,
            OneMinusConstantColor,
            ConstantAlpha,
            OneMinusConstantAlpha,
            SrcAlphaSaturate,
            Src1Color,
            OneMinusSrc1Color,
            Src1Alpha,
            OneMinusSrc1Alpha,
        };

        enum ColorMaskComponent
        {
            R,
            G,
            B,
            A,
        };

        typedef QFlags<QSGMaterialRhiShader::GraphicsPipelineState::ColorMaskComponent> ColorMask;

        enum CullMode
        {
            CullNone,
            CullFront,
            CullBack,
        };
    };

    enum Flag
    {
        UpdatesGraphicsPipelineState,
    };

    typedef QFlags<QSGMaterialRhiShader::Flag> Flags;
    QSGMaterialRhiShader();
    virtual ~QSGMaterialRhiShader();
    virtual bool updateUniformData(QSGMaterialRhiShader::RenderState &state, QSGMaterial *newMaterial, QSGMaterial *oldMaterial);
    virtual void updateSampledImage(QSGMaterialRhiShader::RenderState &state, int binding, QSGTexture **texture, QSGMaterial *newMaterial, QSGMaterial *oldMaterial);
    virtual bool updateGraphicsPipelineState(QSGMaterialRhiShader::RenderState &state, QSGMaterialRhiShader::GraphicsPipelineState *ps, QSGMaterial *newMaterial, QSGMaterial *oldMaterial);
    QSGMaterialRhiShader::Flags flags() const;
    void setFlag(QSGMaterialRhiShader::Flags flags, bool on = true);
};

%End
%If (Qt_5_14_0 -)
QFlags<QSGMaterialRhiShader::GraphicsPipelineState::ColorMaskComponent> operator|(QSGMaterialRhiShader::GraphicsPipelineState::ColorMaskComponent f1, QFlags<QSGMaterialRhiShader::GraphicsPipelineState::ColorMaskComponent> f2);
%End
%If (Qt_5_14_0 -)
QFlags<QSGMaterialRhiShader::Flag> operator|(QSGMaterialRhiShader::Flag f1, QFlags<QSGMaterialRhiShader::Flag> f2);
%End
