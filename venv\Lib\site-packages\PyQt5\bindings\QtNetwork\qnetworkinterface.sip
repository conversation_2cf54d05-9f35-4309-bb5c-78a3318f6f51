// qnetworkinterface.sip generated by MetaSIP
//
// This file is part of the QtNetwork Python extension module.
//
// Copyright (c) 2024 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt5.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


class QNetworkAddressEntry
{
%TypeHeaderCode
#include <qnetworkinterface.h>
%End

public:
    QNetworkAddressEntry();
    QNetworkAddressEntry(const QNetworkAddressEntry &other);
    ~QNetworkAddressEntry();
    QHostAddress ip() const;
    void setIp(const QHostAddress &newIp);
    QHostAddress netmask() const;
    void setNetmask(const QHostAddress &newNetmask);
    QHostAddress broadcast() const;
    void setBroadcast(const QHostAddress &newBroadcast);
    bool operator==(const QNetworkAddressEntry &other) const;
    bool operator!=(const QNetworkAddressEntry &other) const;
    int prefixLength() const;
    void setPrefixLength(int length);
    void swap(QNetworkAddressEntry &other /Constrained/);
%If (Qt_5_11_0 -)

    enum DnsEligibilityStatus
    {
        DnsEligibilityUnknown,
        DnsIneligible,
        DnsEligible,
    };

%End
%If (Qt_5_11_0 -)
    QNetworkAddressEntry::DnsEligibilityStatus dnsEligibility() const;
%End
%If (Qt_5_11_0 -)
    void setDnsEligibility(QNetworkAddressEntry::DnsEligibilityStatus status);
%End
%If (Qt_5_11_0 -)
    bool isLifetimeKnown() const;
%End
%If (Qt_5_11_0 -)
    QDeadlineTimer preferredLifetime() const;
%End
%If (Qt_5_11_0 -)
    QDeadlineTimer validityLifetime() const;
%End
%If (Qt_5_11_0 -)
    void setAddressLifetime(QDeadlineTimer preferred, QDeadlineTimer validity);
%End
%If (Qt_5_11_0 -)
    void clearAddressLifetime();
%End
%If (Qt_5_11_0 -)
    bool isPermanent() const;
%End
%If (Qt_5_11_0 -)
    bool isTemporary() const;
%End
};

class QNetworkInterface
{
%TypeHeaderCode
#include <qnetworkinterface.h>
%End

public:
    enum InterfaceFlag
    {
        IsUp,
        IsRunning,
        CanBroadcast,
        IsLoopBack,
        IsPointToPoint,
        CanMulticast,
    };

    typedef QFlags<QNetworkInterface::InterfaceFlag> InterfaceFlags;
    QNetworkInterface();
    QNetworkInterface(const QNetworkInterface &other);
    ~QNetworkInterface();
    bool isValid() const;
    QString name() const;
    QNetworkInterface::InterfaceFlags flags() const;
    QString hardwareAddress() const;
    QList<QNetworkAddressEntry> addressEntries() const;
    static QNetworkInterface interfaceFromName(const QString &name);
    static QNetworkInterface interfaceFromIndex(int index);
    static QList<QNetworkInterface> allInterfaces();
    static QList<QHostAddress> allAddresses();
    int index() const;
    QString humanReadableName() const;
    void swap(QNetworkInterface &other /Constrained/);
%If (Qt_5_7_0 -)
    static int interfaceIndexFromName(const QString &name);
%End
%If (Qt_5_7_0 -)
    static QString interfaceNameFromIndex(int index);
%End
%If (Qt_5_11_0 -)

    enum InterfaceType
    {
        Unknown,
        Loopback,
        Virtual,
        Ethernet,
        Slip,
        CanBus,
        Ppp,
        Fddi,
        Wifi,
        Ieee80211,
        Phonet,
        Ieee802154,
        SixLoWPAN,
        Ieee80216,
        Ieee1394,
    };

%End
%If (Qt_5_11_0 -)
    QNetworkInterface::InterfaceType type() const;
%End
%If (Qt_5_11_0 -)
    int maximumTransmissionUnit() const;
%End
};

QFlags<QNetworkInterface::InterfaceFlag> operator|(QNetworkInterface::InterfaceFlag f1, QFlags<QNetworkInterface::InterfaceFlag> f2);
