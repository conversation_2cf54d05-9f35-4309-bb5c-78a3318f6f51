#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Widget de base pour tous les composants GSCOM
Fournit des fonctionnalités communes et un style cohérent
"""

import logging
from PyQt5.QtWidgets import *
from PyQt5.QtCore import *
from PyQt5.QtGui import *

class BaseWidget(QWidget):
    """Widget de base avec fonctionnalités communes"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.logger = logging.getLogger(self.__class__.__name__)
        self.setup_ui()
        self.apply_base_styles()
    
    def setup_ui(self):
        """À surcharger dans les classes dérivées"""
        pass
    
    def apply_base_styles(self):
        """Applique les styles de base"""
        self.setStyleSheet("""
            QWidget {
                background-color: transparent;
                color: #ffffff;
                font-family: 'Segoe UI', sans-serif;
            }
            
            QPushButton {
                background: rgba(255, 255, 255, 0.1);
                border: 1px solid rgba(255, 255, 255, 0.2);
                border-radius: 6px;
                padding: 8px 16px;
                color: white;
                font-weight: 500;
            }
            
            QPushButton:hover {
                background: rgba(0, 212, 255, 0.2);
                border-color: #00d4ff;
            }
            
            QPushButton:pressed {
                background: rgba(0, 212, 255, 0.3);
            }
            
            QPushButton:disabled {
                background: rgba(255, 255, 255, 0.05);
                border-color: rgba(255, 255, 255, 0.1);
                color: rgba(255, 255, 255, 0.5);
            }
            
            QLineEdit, QTextEdit, QComboBox, QSpinBox, QDoubleSpinBox {
                background: rgba(255, 255, 255, 0.1);
                border: 1px solid rgba(255, 255, 255, 0.2);
                border-radius: 6px;
                padding: 8px 12px;
                color: white;
                selection-background-color: rgba(0, 212, 255, 0.3);
            }
            
            QLineEdit:focus, QTextEdit:focus, QComboBox:focus {
                border-color: #00d4ff;
                background: rgba(255, 255, 255, 0.15);
            }
            
            QTableWidget {
                background: rgba(255, 255, 255, 0.05);
                border: 1px solid rgba(255, 255, 255, 0.1);
                border-radius: 8px;
                gridline-color: rgba(255, 255, 255, 0.1);
                selection-background-color: rgba(0, 212, 255, 0.2);
            }
            
            QTableWidget::item {
                padding: 8px;
                border-bottom: 1px solid rgba(255, 255, 255, 0.05);
            }
            
            QTableWidget::item:selected {
                background: rgba(0, 212, 255, 0.2);
            }
            
            QHeaderView::section {
                background: rgba(0, 0, 0, 0.3);
                border: none;
                border-bottom: 1px solid rgba(255, 255, 255, 0.1);
                padding: 10px;
                font-weight: bold;
                color: #00d4ff;
            }
            
            QScrollBar:vertical {
                background: rgba(255, 255, 255, 0.1);
                width: 12px;
                border-radius: 6px;
            }
            
            QScrollBar::handle:vertical {
                background: rgba(0, 212, 255, 0.5);
                border-radius: 6px;
                min-height: 20px;
            }
            
            QScrollBar::handle:vertical:hover {
                background: rgba(0, 212, 255, 0.7);
            }
        """)

class ModuleWidget(BaseWidget):
    """Widget de base pour les modules"""
    
    # Signaux
    data_changed = pyqtSignal()
    item_selected = pyqtSignal(object)
    action_requested = pyqtSignal(str, object)
    
    def __init__(self, title="Module", parent=None):
        self.module_title = title
        self.current_item = None
        super().__init__(parent)
    
    def setup_ui(self):
        """Configuration de l'interface du module"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(0, 0, 0, 0)
        layout.setSpacing(10)
        
        # En-tête du module
        self.create_header(layout)
        
        # Barre d'outils
        self.create_toolbar(layout)
        
        # Zone de contenu principal
        self.create_content_area(layout)
        
        # Barre de statut du module
        self.create_status_bar(layout)
    
    def create_header(self, layout):
        """Crée l'en-tête du module"""
        header = QFrame()
        header.setObjectName("moduleHeader")
        header.setFixedHeight(60)
        
        header_layout = QHBoxLayout(header)
        header_layout.setContentsMargins(20, 10, 20, 10)
        
        # Titre du module
        title_label = QLabel(self.module_title)
        title_label.setObjectName("moduleTitle")
        title_label.setStyleSheet("""
            #moduleTitle {
                font-size: 24px;
                font-weight: bold;
                color: #00d4ff;
            }
        """)
        header_layout.addWidget(title_label)
        
        header_layout.addStretch()
        
        # Compteur d'éléments
        self.count_label = QLabel("0 éléments")
        self.count_label.setObjectName("countLabel")
        self.count_label.setStyleSheet("""
            #countLabel {
                color: rgba(255, 255, 255, 0.7);
                font-size: 12px;
            }
        """)
        header_layout.addWidget(self.count_label)
        
        layout.addWidget(header)
    
    def create_toolbar(self, layout):
        """Crée la barre d'outils"""
        toolbar = QFrame()
        toolbar.setObjectName("moduleToolbar")
        toolbar.setFixedHeight(50)
        
        toolbar_layout = QHBoxLayout(toolbar)
        toolbar_layout.setContentsMargins(20, 5, 20, 5)
        
        # Boutons d'action
        self.new_button = QPushButton("➕ Nouveau")
        self.new_button.setObjectName("actionButton")
        self.new_button.clicked.connect(lambda: self.handle_action("new"))
        toolbar_layout.addWidget(self.new_button)
        
        self.edit_button = QPushButton("✏️ Modifier")
        self.edit_button.setObjectName("actionButton")
        self.edit_button.setEnabled(False)
        self.edit_button.clicked.connect(lambda: self.handle_action("edit"))
        toolbar_layout.addWidget(self.edit_button)
        
        self.delete_button = QPushButton("🗑️ Supprimer")
        self.delete_button.setObjectName("actionButton")
        self.delete_button.setEnabled(False)
        self.delete_button.clicked.connect(lambda: self.handle_action("delete"))
        toolbar_layout.addWidget(self.delete_button)
        
        toolbar_layout.addWidget(QFrame())  # Séparateur
        
        # Barre de recherche
        self.search_input = QLineEdit()
        self.search_input.setPlaceholderText("🔍 Rechercher...")
        self.search_input.setFixedWidth(250)
        self.search_input.textChanged.connect(self.filter_data)
        toolbar_layout.addWidget(self.search_input)
        
        toolbar_layout.addStretch()
        
        # Bouton d'actualisation
        self.refresh_button = QPushButton("🔄 Actualiser")
        self.refresh_button.setObjectName("actionButton")
        self.refresh_button.clicked.connect(self.refresh_data)
        toolbar_layout.addWidget(self.refresh_button)
        
        layout.addWidget(toolbar)
    
    def create_content_area(self, layout):
        """Crée la zone de contenu principal"""
        self.content_area = QFrame()
        self.content_area.setObjectName("contentArea")
        
        self.content_layout = QVBoxLayout(self.content_area)
        self.content_layout.setContentsMargins(20, 10, 20, 10)
        
        # Table de données par défaut
        self.create_data_table()
        
        layout.addWidget(self.content_area)
    
    def create_data_table(self):
        """Crée le tableau de données"""
        self.data_table = QTableWidget()
        self.data_table.setObjectName("dataTable")
        self.data_table.setAlternatingRowColors(True)
        self.data_table.setSelectionBehavior(QAbstractItemView.SelectRows)
        self.data_table.setSelectionMode(QAbstractItemView.SingleSelection)
        self.data_table.setSortingEnabled(True)
        
        # Connecter les signaux
        self.data_table.itemSelectionChanged.connect(self.on_selection_changed)
        self.data_table.itemDoubleClicked.connect(lambda: self.handle_action("edit"))
        
        self.content_layout.addWidget(self.data_table)
    
    def create_status_bar(self, layout):
        """Crée la barre de statut du module"""
        status_bar = QFrame()
        status_bar.setObjectName("moduleStatusBar")
        status_bar.setFixedHeight(30)
        
        status_layout = QHBoxLayout(status_bar)
        status_layout.setContentsMargins(20, 5, 20, 5)
        
        self.status_label = QLabel("Prêt")
        self.status_label.setObjectName("statusLabel")
        self.status_label.setStyleSheet("""
            #statusLabel {
                color: rgba(255, 255, 255, 0.7);
                font-size: 11px;
            }
        """)
        status_layout.addWidget(self.status_label)
        
        status_layout.addStretch()
        
        # Indicateur de chargement
        self.loading_label = QLabel()
        self.loading_label.setObjectName("loadingLabel")
        self.loading_label.hide()
        status_layout.addWidget(self.loading_label)
        
        layout.addWidget(status_bar)
    
    def on_selection_changed(self):
        """Gère le changement de sélection"""
        selected_items = self.data_table.selectedItems()
        has_selection = len(selected_items) > 0
        
        self.edit_button.setEnabled(has_selection)
        self.delete_button.setEnabled(has_selection)
        
        if has_selection:
            row = self.data_table.currentRow()
            self.current_item = self.get_item_from_row(row)
            self.item_selected.emit(self.current_item)
        else:
            self.current_item = None
    
    def handle_action(self, action):
        """Gère les actions du module"""
        self.action_requested.emit(action, self.current_item)
    
    def filter_data(self, text):
        """Filtre les données selon le texte de recherche"""
        for row in range(self.data_table.rowCount()):
            match = False
            for col in range(self.data_table.columnCount()):
                item = self.data_table.item(row, col)
                if item and text.lower() in item.text().lower():
                    match = True
                    break
            self.data_table.setRowHidden(row, not match)
    
    def refresh_data(self):
        """Actualise les données"""
        self.show_loading(True)
        self.load_data()
        self.show_loading(False)
    
    def load_data(self):
        """À surcharger pour charger les données spécifiques"""
        pass
    
    def get_item_from_row(self, row):
        """À surcharger pour récupérer l'objet depuis la ligne"""
        return None
    
    def update_count(self, count):
        """Met à jour le compteur d'éléments"""
        self.count_label.setText(f"{count} élément{'s' if count != 1 else ''}")
    
    def show_loading(self, show=True):
        """Affiche/masque l'indicateur de chargement"""
        if show:
            self.loading_label.setText("⏳ Chargement...")
            self.loading_label.show()
            self.status_label.setText("Chargement en cours...")
        else:
            self.loading_label.hide()
            self.status_label.setText("Prêt")
    
    def show_message(self, message, message_type="info"):
        """Affiche un message dans la barre de statut"""
        icons = {
            "info": "ℹ️",
            "success": "✅",
            "warning": "⚠️",
            "error": "❌"
        }
        
        icon = icons.get(message_type, "ℹ️")
        self.status_label.setText(f"{icon} {message}")
        
        # Effacer le message après 3 secondes
        QTimer.singleShot(3000, lambda: self.status_label.setText("Prêt"))

class FormDialog(QDialog):
    """Boîte de dialogue de base pour les formulaires"""
    
    def __init__(self, title="Formulaire", parent=None):
        super().__init__(parent)
        self.setWindowTitle(title)
        self.setModal(True)
        self.setFixedSize(500, 400)
        self.setup_ui()
        self.apply_styles()
    
    def setup_ui(self):
        """Configuration de l'interface"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(15)
        
        # Zone de formulaire
        self.form_area = QScrollArea()
        self.form_area.setWidgetResizable(True)
        self.form_area.setHorizontalScrollBarPolicy(Qt.ScrollBarAlwaysOff)
        
        self.form_widget = QWidget()
        self.form_layout = QFormLayout(self.form_widget)
        self.form_layout.setSpacing(10)
        
        self.form_area.setWidget(self.form_widget)
        layout.addWidget(self.form_area)
        
        # Boutons
        button_layout = QHBoxLayout()
        button_layout.addStretch()
        
        self.cancel_button = QPushButton("Annuler")
        self.cancel_button.clicked.connect(self.reject)
        button_layout.addWidget(self.cancel_button)
        
        self.save_button = QPushButton("Enregistrer")
        self.save_button.setObjectName("primaryButton")
        self.save_button.clicked.connect(self.accept)
        button_layout.addWidget(self.save_button)
        
        layout.addLayout(button_layout)
    
    def apply_styles(self):
        """Applique les styles"""
        self.setStyleSheet("""
            QDialog {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(10, 10, 30, 0.95),
                    stop:1 rgba(20, 20, 50, 0.95));
                border: 1px solid rgba(255, 255, 255, 0.2);
                border-radius: 12px;
            }
            
            #primaryButton {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #00d4ff, stop:1 #ff00ff);
                border: none;
                border-radius: 6px;
                color: white;
                font-weight: bold;
                padding: 10px 20px;
            }
            
            #primaryButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #00b8e6, stop:1 #e600e6);
            }
            
            QLabel {
                color: rgba(255, 255, 255, 0.9);
                font-weight: 500;
            }
            
            QScrollArea {
                border: none;
                background: transparent;
            }
        """)
    
    def add_field(self, label, widget):
        """Ajoute un champ au formulaire"""
        self.form_layout.addRow(label, widget)
        return widget
