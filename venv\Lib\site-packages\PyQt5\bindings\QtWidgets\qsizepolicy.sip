// qsizepolicy.sip generated by MetaSIP
//
// This file is part of the QtWidgets Python extension module.
//
// Copyright (c) 2024 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt5.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


class QSizePolicy
{
%TypeHeaderCode
#include <qsizepolicy.h>
%End

public:
    enum PolicyFlag
    {
        GrowFlag,
        ExpandFlag,
        ShrinkFlag,
        IgnoreFlag,
    };

    enum Policy
    {
        Fixed,
        Minimum,
        Maximum,
        Preferred,
        MinimumExpanding,
        Expanding,
        Ignored,
    };

    QSizePolicy();
    QSizePolicy(QSizePolicy::Policy horizontal, QSizePolicy::Policy vertical, QSizePolicy::ControlType type = QSizePolicy::DefaultType);
    QSizePolicy(const QVariant &variant /GetWrapper/) /NoDerived/;
%MethodCode
        if (a0->canConvert<QSizePolicy>())
            sipCpp = new QSizePolicy(a0->value<QSizePolicy>());
        else
            sipError = sipBadCallableArg(0, a0Wrapper);
%End

    QSizePolicy::Policy horizontalPolicy() const;
    QSizePolicy::Policy verticalPolicy() const;
    void setHorizontalPolicy(QSizePolicy::Policy d);
    void setVerticalPolicy(QSizePolicy::Policy d);
    Qt::Orientations expandingDirections() const;
    void setHeightForWidth(bool b);
    bool hasHeightForWidth() const;
    bool operator==(const QSizePolicy &s) const;
    bool operator!=(const QSizePolicy &s) const;
    int horizontalStretch() const;
    int verticalStretch() const;
    void setHorizontalStretch(int stretchFactor);
    void setVerticalStretch(int stretchFactor);
    void transpose();
%If (Qt_5_9_0 -)
    QSizePolicy transposed() const;
%End

    enum ControlType
    {
        DefaultType,
        ButtonBox,
        CheckBox,
        ComboBox,
        Frame,
        GroupBox,
        Label,
        Line,
        LineEdit,
        PushButton,
        RadioButton,
        Slider,
        SpinBox,
        TabWidget,
        ToolButton,
    };

    typedef QFlags<QSizePolicy::ControlType> ControlTypes;
    QSizePolicy::ControlType controlType() const;
    void setControlType(QSizePolicy::ControlType type);
    void setWidthForHeight(bool b);
    bool hasWidthForHeight() const;
%If (Qt_5_2_0 -)
    bool retainSizeWhenHidden() const;
%End
%If (Qt_5_2_0 -)
    void setRetainSizeWhenHidden(bool retainSize);
%End
%If (Qt_5_6_0 -)
    long __hash__() const;
%MethodCode
        sipRes = qHash(*sipCpp);
%End

%End
};

QDataStream &operator<<(QDataStream &, const QSizePolicy & /Constrained/) /ReleaseGIL/;
QDataStream &operator>>(QDataStream &, QSizePolicy & /Constrained/) /ReleaseGIL/;
QFlags<QSizePolicy::ControlType> operator|(QSizePolicy::ControlType f1, QFlags<QSizePolicy::ControlType> f2);
