#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Module de gestion des produits
Interface complète pour la gestion du catalogue produits
"""

import logging
from PyQt5.QtWidgets import *
from PyQt5.QtCore import *
from PyQt5.QtGui import *

from src.ui.components.base_widget import ModuleWidget, FormDialog
from src.dal.models.product import Product, Category, Unit, ProductType, ProductStatus
from src.dal.database import db_manager

class ProductsWidget(ModuleWidget):
    """Widget principal pour la gestion des produits"""

    def __init__(self, parent=None):
        super().__init__("Gestion des Produits", parent)
        self.products = []
        self.categories = []
        self.units = []
        self.load_data()

    def create_data_table(self):
        """Crée le tableau des produits"""
        super().create_data_table()

        # Configuration des colonnes
        headers = ["Code", "Nom", "Catégorie", "Prix Achat", "Prix Vente", "Stock", "Statut"]
        self.data_table.setColumnCount(len(headers))
        self.data_table.setHorizontalHeaderLabels(headers)

        # Ajuster la largeur des colonnes
        header = self.data_table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.ResizeToContents)  # Code
        header.setSectionResizeMode(1, QHeaderView.Stretch)           # Nom
        header.setSectionResizeMode(2, QHeaderView.ResizeToContents)  # Catégorie
        header.setSectionResizeMode(3, QHeaderView.ResizeToContents)  # Prix Achat
        header.setSectionResizeMode(4, QHeaderView.ResizeToContents)  # Prix Vente
        header.setSectionResizeMode(5, QHeaderView.ResizeToContents)  # Stock
        header.setSectionResizeMode(6, QHeaderView.ResizeToContents)  # Statut

        # Menu contextuel
        self.data_table.setContextMenuPolicy(Qt.CustomContextMenu)
        self.data_table.customContextMenuRequested.connect(self.show_context_menu)

    def load_data(self):
        """Charge les données des produits"""
        try:
            with db_manager.get_session() as session:
                # Charger les produits avec leurs relations
                products = session.query(Product).all()
                categories = session.query(Category).all()
                units = session.query(Unit).all()

                # Créer des copies détachées des objets
                self.products = []
                for product in products:
                    # Forcer le chargement des relations
                    _ = product.category
                    _ = product.unit
                    self.products.append(product)

                self.categories = []
                for category in categories:
                    self.categories.append(category)

                self.units = []
                for unit in units:
                    self.units.append(unit)

                # Détacher les objets de la session
                session.expunge_all()

                self.populate_table()
                self.update_count(len(self.products))
                self.show_message(f"{len(self.products)} produits chargés", "success")
        except Exception as e:
            self.logger.error(f"Erreur lors du chargement des produits: {e}")
            self.show_message("Erreur lors du chargement", "error")

    def populate_table(self):
        """Remplit le tableau avec les données"""
        self.data_table.setRowCount(len(self.products))

        for row, product in enumerate(self.products):
            # Code
            self.data_table.setItem(row, 0, QTableWidgetItem(product.code))

            # Nom
            self.data_table.setItem(row, 1, QTableWidgetItem(product.name))

            # Catégorie
            category_name = product.category.name if product.category else "Sans catégorie"
            self.data_table.setItem(row, 2, QTableWidgetItem(category_name))

            # Prix d'achat
            purchase_price = f"{product.purchase_price:.2f} DA" if product.purchase_price else "N/A"
            self.data_table.setItem(row, 3, QTableWidgetItem(purchase_price))

            # Prix de vente
            sale_price = f"{product.sale_price:.2f} DA" if product.sale_price else "N/A"
            self.data_table.setItem(row, 4, QTableWidgetItem(sale_price))

            # Stock
            if product.track_stock:
                stock_text = f"{product.current_stock:.0f}"
                stock_item = QTableWidgetItem(stock_text)

                # Colorer selon le niveau de stock
                if product.is_out_of_stock:
                    stock_item.setForeground(QColor("#ff4444"))
                elif product.is_low_stock:
                    stock_item.setForeground(QColor("#ffaa00"))
                else:
                    stock_item.setForeground(QColor("#00ff88"))
            else:
                stock_item = QTableWidgetItem("N/A")
                stock_item.setForeground(QColor("#888888"))

            self.data_table.setItem(row, 5, stock_item)

            # Statut
            status_text = "Actif" if product.status == ProductStatus.ACTIVE else "Inactif"
            status_item = QTableWidgetItem(status_text)
            if product.status == ProductStatus.ACTIVE:
                status_item.setForeground(QColor("#00ff88"))
            else:
                status_item.setForeground(QColor("#ff4444"))
            self.data_table.setItem(row, 6, status_item)

    def get_item_from_row(self, row):
        """Récupère le produit depuis la ligne sélectionnée"""
        if 0 <= row < len(self.products):
            return self.products[row]
        return None

    def handle_action(self, action):
        """Gère les actions du module"""
        if action == "new":
            self.new_product()
        elif action == "edit" and self.current_item:
            self.edit_product(self.current_item)
        elif action == "delete" and self.current_item:
            self.delete_product(self.current_item)

        super().handle_action(action)

    def new_product(self):
        """Crée un nouveau produit"""
        dialog = ProductFormDialog("Nouveau Produit", self.categories, self.units, self)
        if dialog.exec_() == QDialog.Accepted:
            product_data = dialog.get_data()
            if self.save_product(product_data):
                self.refresh_data()

    def edit_product(self, product):
        """Modifie un produit existant"""
        dialog = ProductFormDialog("Modifier Produit", self.categories, self.units, self)
        dialog.set_data(product)
        if dialog.exec_() == QDialog.Accepted:
            product_data = dialog.get_data()
            product_data['id'] = product.id
            if self.save_product(product_data, is_edit=True):
                self.refresh_data()

    def delete_product(self, product):
        """Supprime un produit"""
        reply = QMessageBox.question(
            self,
            "Confirmer la suppression",
            f"Êtes-vous sûr de vouloir supprimer le produit '{product.name}' ?\n\n"
            "Cette action est irréversible.",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )

        if reply == QMessageBox.Yes:
            try:
                with db_manager.get_session() as session:
                    product_to_delete = session.query(Product).filter(Product.id == product.id).first()
                    if product_to_delete:
                        session.delete(product_to_delete)
                        session.commit()
                        self.show_message(f"Produit '{product.name}' supprimé", "success")
                        self.refresh_data()
            except Exception as e:
                self.logger.error(f"Erreur lors de la suppression: {e}")
                self.show_message("Erreur lors de la suppression", "error")

    def save_product(self, data, is_edit=False):
        """Sauvegarde un produit"""
        try:
            with db_manager.get_session() as session:
                if is_edit:
                    product = session.query(Product).filter(Product.id == data['id']).first()
                    if not product:
                        self.show_message("Produit introuvable", "error")
                        return False

                    # Mettre à jour les champs
                    for key, value in data.items():
                        if key != 'id' and hasattr(product, key):
                            setattr(product, key, value)
                else:
                    # Générer un code produit automatique
                    if not data.get('code'):
                        last_product = session.query(Product).order_by(Product.id.desc()).first()
                        next_id = (last_product.id + 1) if last_product else 1
                        data['code'] = f"PRD{next_id:06d}"

                    product = Product(**data)
                    session.add(product)

                session.commit()
                action = "modifié" if is_edit else "créé"
                self.show_message(f"Produit {action} avec succès", "success")
                return True

        except Exception as e:
            self.logger.error(f"Erreur lors de la sauvegarde: {e}")
            self.show_message("Erreur lors de la sauvegarde", "error")
            return False

    def show_context_menu(self, position):
        """Affiche le menu contextuel"""
        if self.data_table.itemAt(position):
            menu = QMenu(self)

            edit_action = menu.addAction("✏️ Modifier")
            edit_action.triggered.connect(lambda: self.handle_action("edit"))

            delete_action = menu.addAction("🗑️ Supprimer")
            delete_action.triggered.connect(lambda: self.handle_action("delete"))

            menu.addSeparator()

            view_action = menu.addAction("👁️ Voir détails")
            view_action.triggered.connect(self.view_product_details)

            stock_action = menu.addAction("📦 Gérer stock")
            stock_action.triggered.connect(self.manage_stock)

            menu.exec_(self.data_table.mapToGlobal(position))

    def view_product_details(self):
        """Affiche les détails du produit"""
        if self.current_item:
            dialog = ProductDetailsDialog(self.current_item, self)
            dialog.exec_()

    def manage_stock(self):
        """Gère le stock du produit"""
        if self.current_item:
            QMessageBox.information(self, "Gestion du stock",
                                  f"Gestion du stock pour '{self.current_item.name}'\n"
                                  "Fonctionnalité en cours de développement.")

class ProductFormDialog(FormDialog):
    """Formulaire de saisie/modification produit"""

    def __init__(self, title, categories, units, parent=None):
        self.categories = categories
        self.units = units
        super().__init__(title, parent)
        self.setFixedSize(600, 700)
        self.setup_form()

    def setup_form(self):
        """Configure le formulaire"""
        # Informations de base
        self.code_input = QLineEdit()
        self.code_input.setPlaceholderText("Généré automatiquement si vide")
        self.add_field("Code:", self.code_input)

        self.name_input = QLineEdit()
        self.name_input.setPlaceholderText("Nom du produit")
        self.add_field("Nom *:", self.name_input)

        self.description_input = QTextEdit()
        self.description_input.setMaximumHeight(60)
        self.description_input.setPlaceholderText("Description du produit")
        self.add_field("Description:", self.description_input)

        # Classification
        self.category_combo = QComboBox()
        self.category_combo.addItem("Aucune catégorie", None)
        for category in self.categories:
            self.category_combo.addItem(category.name, category.id)
        self.add_field("Catégorie:", self.category_combo)

        self.unit_combo = QComboBox()
        self.unit_combo.addItem("Aucune unité", None)
        for unit in self.units:
            self.unit_combo.addItem(f"{unit.name} ({unit.symbol})", unit.id)
        self.add_field("Unité:", self.unit_combo)

        self.type_combo = QComboBox()
        self.type_combo.addItems(["Produit", "Service", "Consommable"])
        self.add_field("Type:", self.type_combo)

        # Code-barres
        self.barcode_input = QLineEdit()
        self.barcode_input.setPlaceholderText("Code-barres (optionnel)")
        self.add_field("Code-barres:", self.barcode_input)

        # Prix
        self.purchase_price_input = QDoubleSpinBox()
        self.purchase_price_input.setMaximum(*********.99)
        self.purchase_price_input.setSuffix(" DA")
        self.add_field("Prix d'achat:", self.purchase_price_input)

        self.sale_price_input = QDoubleSpinBox()
        self.sale_price_input.setMaximum(*********.99)
        self.sale_price_input.setSuffix(" DA")
        self.add_field("Prix de vente:", self.sale_price_input)

        self.min_sale_price_input = QDoubleSpinBox()
        self.min_sale_price_input.setMaximum(*********.99)
        self.min_sale_price_input.setSuffix(" DA")
        self.add_field("Prix min. vente:", self.min_sale_price_input)

        # TVA
        self.tax_rate_input = QDoubleSpinBox()
        self.tax_rate_input.setMaximum(100.0)
        self.tax_rate_input.setValue(19.0)
        self.tax_rate_input.setSuffix(" %")
        self.add_field("Taux TVA:", self.tax_rate_input)

        self.tax_included_checkbox = QCheckBox("Prix TTC")
        self.tax_included_checkbox.setChecked(True)
        self.add_field("", self.tax_included_checkbox)

        # Stock
        self.track_stock_checkbox = QCheckBox("Suivre le stock")
        self.track_stock_checkbox.setChecked(True)
        self.track_stock_checkbox.toggled.connect(self.toggle_stock_fields)
        self.add_field("", self.track_stock_checkbox)

        self.current_stock_input = QDoubleSpinBox()
        self.current_stock_input.setMaximum(*********.999)
        self.current_stock_input.setDecimals(3)
        self.add_field("Stock actuel:", self.current_stock_input)

        self.min_stock_input = QDoubleSpinBox()
        self.min_stock_input.setMaximum(*********.999)
        self.min_stock_input.setDecimals(3)
        self.add_field("Stock minimum:", self.min_stock_input)

        self.max_stock_input = QDoubleSpinBox()
        self.max_stock_input.setMaximum(*********.999)
        self.max_stock_input.setDecimals(3)
        self.add_field("Stock maximum:", self.max_stock_input)

        # Statut
        self.status_combo = QComboBox()
        self.status_combo.addItems(["Actif", "Inactif", "Arrêté"])
        self.add_field("Statut:", self.status_combo)

        # Notes
        self.notes_input = QTextEdit()
        self.notes_input.setMaximumHeight(80)
        self.notes_input.setPlaceholderText("Notes et commentaires...")
        self.add_field("Notes:", self.notes_input)

    def toggle_stock_fields(self, enabled):
        """Active/désactive les champs de stock"""
        self.current_stock_input.setEnabled(enabled)
        self.min_stock_input.setEnabled(enabled)
        self.max_stock_input.setEnabled(enabled)

    def get_data(self):
        """Récupère les données du formulaire"""
        # Mapping des types
        type_mapping = {
            "Produit": ProductType.PRODUCT,
            "Service": ProductType.SERVICE,
            "Consommable": ProductType.CONSUMABLE
        }

        status_mapping = {
            "Actif": ProductStatus.ACTIVE,
            "Inactif": ProductStatus.INACTIVE,
            "Arrêté": ProductStatus.DISCONTINUED
        }

        return {
            'code': self.code_input.text().strip() or None,
            'name': self.name_input.text().strip(),
            'description': self.description_input.toPlainText().strip() or None,
            'barcode': self.barcode_input.text().strip() or None,
            'category_id': self.category_combo.currentData(),
            'unit_id': self.unit_combo.currentData(),
            'product_type': type_mapping[self.type_combo.currentText()],
            'status': status_mapping[self.status_combo.currentText()],
            'purchase_price': self.purchase_price_input.value(),
            'sale_price': self.sale_price_input.value(),
            'min_sale_price': self.min_sale_price_input.value(),
            'tax_rate': self.tax_rate_input.value(),
            'tax_included': self.tax_included_checkbox.isChecked(),
            'track_stock': self.track_stock_checkbox.isChecked(),
            'current_stock': self.current_stock_input.value() if self.track_stock_checkbox.isChecked() else 0,
            'min_stock': self.min_stock_input.value() if self.track_stock_checkbox.isChecked() else 0,
            'max_stock': self.max_stock_input.value() if self.track_stock_checkbox.isChecked() else 0,
            'notes': self.notes_input.toPlainText().strip() or None
        }

    def set_data(self, product):
        """Remplit le formulaire avec les données du produit"""
        self.code_input.setText(product.code or "")
        self.name_input.setText(product.name or "")
        self.description_input.setPlainText(product.description or "")
        self.barcode_input.setText(product.barcode or "")

        # Catégorie
        if product.category_id:
            for i in range(self.category_combo.count()):
                if self.category_combo.itemData(i) == product.category_id:
                    self.category_combo.setCurrentIndex(i)
                    break

        # Unité
        if product.unit_id:
            for i in range(self.unit_combo.count()):
                if self.unit_combo.itemData(i) == product.unit_id:
                    self.unit_combo.setCurrentIndex(i)
                    break

        # Type
        type_text = {
            ProductType.PRODUCT: "Produit",
            ProductType.SERVICE: "Service",
            ProductType.CONSUMABLE: "Consommable"
        }.get(product.product_type, "Produit")
        self.type_combo.setCurrentText(type_text)

        # Statut
        status_text = {
            ProductStatus.ACTIVE: "Actif",
            ProductStatus.INACTIVE: "Inactif",
            ProductStatus.DISCONTINUED: "Arrêté"
        }.get(product.status, "Actif")
        self.status_combo.setCurrentText(status_text)

        # Prix
        self.purchase_price_input.setValue(float(product.purchase_price or 0))
        self.sale_price_input.setValue(float(product.sale_price or 0))
        self.min_sale_price_input.setValue(float(product.min_sale_price or 0))
        self.tax_rate_input.setValue(float(product.tax_rate or 19.0))
        self.tax_included_checkbox.setChecked(product.tax_included)

        # Stock
        self.track_stock_checkbox.setChecked(product.track_stock)
        self.current_stock_input.setValue(float(product.current_stock or 0))
        self.min_stock_input.setValue(float(product.min_stock or 0))
        self.max_stock_input.setValue(float(product.max_stock or 0))

        # Notes
        self.notes_input.setPlainText(product.notes or "")

class ProductDetailsDialog(QDialog):
    """Dialogue d'affichage des détails produit"""

    def __init__(self, product, parent=None):
        super().__init__(parent)
        self.product = product
        self.setWindowTitle(f"Détails - {product.name}")
        self.setFixedSize(600, 500)
        self.setup_ui()
        self.apply_styles()

    def setup_ui(self):
        """Configure l'interface"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(20, 20, 20, 20)

        # En-tête
        header_layout = QHBoxLayout()

        icon_label = QLabel("📦")
        icon_label.setStyleSheet("font-size: 32px;")
        header_layout.addWidget(icon_label)

        title_layout = QVBoxLayout()
        title_label = QLabel(self.product.name)
        title_label.setStyleSheet("font-size: 18px; font-weight: bold; color: #00d4ff;")
        title_layout.addWidget(title_label)

        subtitle_label = QLabel(f"Code: {self.product.code}")
        subtitle_label.setStyleSheet("color: rgba(255, 255, 255, 0.7);")
        title_layout.addWidget(subtitle_label)

        header_layout.addLayout(title_layout)
        header_layout.addStretch()

        layout.addLayout(header_layout)

        # Contenu dans un scroll area
        scroll = QScrollArea()
        scroll.setWidgetResizable(True)

        content_widget = QWidget()
        content_layout = QVBoxLayout(content_widget)

        # Informations générales
        general_info = [
            ("Type", "Produit" if self.product.product_type == ProductType.PRODUCT else "Service"),
            ("Statut", "Actif" if self.product.status == ProductStatus.ACTIVE else "Inactif"),
        ]

        if self.product.description:
            general_info.append(("Description", self.product.description))

        if self.product.barcode:
            general_info.append(("Code-barres", self.product.barcode))

        self.add_section(content_layout, "Informations Générales", general_info)

        # Prix
        price_info = [
            ("Prix d'achat", f"{self.product.purchase_price:.2f} DA" if self.product.purchase_price else "Non défini"),
            ("Prix de vente", f"{self.product.sale_price:.2f} DA" if self.product.sale_price else "Non défini"),
            ("Taux TVA", f"{self.product.tax_rate:.1f} %" if self.product.tax_rate else "Non défini"),
            ("Prix TTC", "Oui" if self.product.tax_included else "Non")
        ]

        if self.product.min_sale_price:
            price_info.insert(2, ("Prix min. vente", f"{self.product.min_sale_price:.2f} DA"))

        self.add_section(content_layout, "Tarification", price_info)

        # Stock
        if self.product.track_stock:
            stock_info = [
                ("Stock actuel", f"{self.product.current_stock:.0f}"),
                ("Stock minimum", f"{self.product.min_stock:.0f}"),
                ("Stock maximum", f"{self.product.max_stock:.0f}"),
                ("État", "Stock bas" if self.product.is_low_stock else "Stock OK")
            ]
            self.add_section(content_layout, "Stock", stock_info)

        # Notes
        if self.product.notes:
            self.add_section(content_layout, "Notes", [
                ("", self.product.notes)
            ])

        scroll.setWidget(content_widget)
        layout.addWidget(scroll)

        # Bouton fermer
        close_button = QPushButton("Fermer")
        close_button.clicked.connect(self.accept)
        layout.addWidget(close_button)

    def add_section(self, layout, title, items):
        """Ajoute une section d'informations"""
        # Titre de section
        title_label = QLabel(title)
        title_label.setStyleSheet("""
            font-size: 14px;
            font-weight: bold;
            color: #00d4ff;
            margin-top: 15px;
            margin-bottom: 5px;
        """)
        layout.addWidget(title_label)

        # Contenu de la section
        for label, value in items:
            item_layout = QHBoxLayout()

            if label:
                label_widget = QLabel(f"{label}:")
                label_widget.setStyleSheet("color: rgba(255, 255, 255, 0.8); font-weight: 500;")
                label_widget.setFixedWidth(150)
                item_layout.addWidget(label_widget)

            value_widget = QLabel(str(value))
            value_widget.setStyleSheet("color: white;")
            value_widget.setWordWrap(True)
            item_layout.addWidget(value_widget)

            layout.addLayout(item_layout)

    def apply_styles(self):
        """Applique les styles"""
        self.setStyleSheet("""
            QDialog {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(10, 10, 30, 0.95),
                    stop:1 rgba(20, 20, 50, 0.95));
                border: 1px solid rgba(255, 255, 255, 0.2);
                border-radius: 12px;
            }

            QScrollArea {
                border: none;
                background: transparent;
            }

            QPushButton {
                background: rgba(255, 255, 255, 0.1);
                border: 1px solid rgba(255, 255, 255, 0.2);
                border-radius: 6px;
                padding: 10px 20px;
                color: white;
                font-weight: 500;
            }

            QPushButton:hover {
                background: rgba(0, 212, 255, 0.2);
                border-color: #00d4ff;
            }
        """)
