// qspinbox.sip generated by MetaSIP
//
// This file is part of the QtWidgets Python extension module.
//
// Copyright (c) 2024 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt5.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


class QSpinBox : public QAbstractSpinBox
{
%TypeHeaderCode
#include <qspinbox.h>
%End

public:
    explicit QSpinBox(QWidget *parent /TransferThis/ = 0);
    virtual ~QSpinBox();
    int value() const;
    QString prefix() const;
    void setPrefix(const QString &p);
    QString suffix() const;
    void setSuffix(const QString &s);
    QString cleanText() const;
    int singleStep() const;
    void setSingleStep(int val);
    int minimum() const;
    void setMinimum(int min);
    int maximum() const;
    void setMaximum(int max);
    void setRange(int min, int max);

protected:
    virtual QValidator::State validate(QString &input /In,Out/, int &pos /In,Out/) const;
    virtual int valueFromText(const QString &text) const;
    virtual QString textFromValue(int v) const;
    virtual void fixup(QString &str /In,Out/) const;
    virtual bool event(QEvent *e);

public slots:
    void setValue(int val);

signals:
    void valueChanged(int);
    void valueChanged(const QString &);
%If (Qt_5_14_0 -)
    void textChanged(const QString &);
%End

public:
%If (Qt_5_2_0 -)
    int displayIntegerBase() const;
%End
%If (Qt_5_2_0 -)
    void setDisplayIntegerBase(int base);
%End
%If (Qt_5_12_0 -)
    QAbstractSpinBox::StepType stepType() const;
%End
%If (Qt_5_12_0 -)
    void setStepType(QAbstractSpinBox::StepType stepType);
%End
};

class QDoubleSpinBox : public QAbstractSpinBox
{
%TypeHeaderCode
#include <qspinbox.h>
%End

public:
    explicit QDoubleSpinBox(QWidget *parent /TransferThis/ = 0);
    virtual ~QDoubleSpinBox();
    double value() const;
    QString prefix() const;
    void setPrefix(const QString &p);
    QString suffix() const;
    void setSuffix(const QString &s);
    QString cleanText() const;
    double singleStep() const;
    void setSingleStep(double val);
    double minimum() const;
    void setMinimum(double min);
    double maximum() const;
    void setMaximum(double max);
    void setRange(double min, double max);
    int decimals() const;
    void setDecimals(int prec);
    virtual QValidator::State validate(QString &input /In,Out/, int &pos /In,Out/) const;
    virtual double valueFromText(const QString &text) const;
    virtual QString textFromValue(double v) const;
    virtual void fixup(QString &str /In,Out/) const;

public slots:
    void setValue(double val);

signals:
    void valueChanged(double);
    void valueChanged(const QString &);
%If (Qt_5_14_0 -)
    void textChanged(const QString &);
%End

public:
%If (Qt_5_12_0 -)
    QAbstractSpinBox::StepType stepType() const;
%End
%If (Qt_5_12_0 -)
    void setStepType(QAbstractSpinBox::StepType stepType);
%End
};
