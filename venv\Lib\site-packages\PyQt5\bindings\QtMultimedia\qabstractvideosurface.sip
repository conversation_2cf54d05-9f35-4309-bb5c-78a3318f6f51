// qabstractvideosurface.sip generated by MetaSIP
//
// This file is part of the QtMultimedia Python extension module.
//
// Copyright (c) 2024 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt5.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


class QAbstractVideoSurface : public QObject
{
%TypeHeaderCode
#include <qabstractvideosurface.h>
%End

%ConvertToSubClassCode
    static struct class_graph {
        const char *name;
        sipTypeDef **type;
        int yes, no;
    } graph[] = {
    #if QT_VERSION >= 0x050500
        {sipName_QAbstractVideoFilter, &sipType_QAbstractVideoFilter, -1, 1},
    #else
        {0, 0, -1, 1},
    #endif
        {sipName_QAbstractVideoSurface, &sipType_QAbstractVideoSurface, -1, 2},
        {sipName_QMediaObject, &sipType_QMediaObject, 18, 3},
        {sipName_QMediaControl, &sipType_QMediaControl, 22, 4},
        {sipName_QAudioInput, &sipType_QAudioInput, -1, 5},
        {sipName_QAudioOutput, &sipType_QAudioOutput, -1, 6},
        {sipName_QAudioProbe, &sipType_QAudioProbe, -1, 7},
        {sipName_QMediaRecorder, &sipType_QMediaRecorder, 60, 8},
        {sipName_QCameraExposure, &sipType_QCameraExposure, -1, 9},
        {sipName_QCameraFocus, &sipType_QCameraFocus, -1, 10},
        {sipName_QCameraImageCapture, &sipType_QCameraImageCapture, -1, 11},
        {sipName_QCameraImageProcessing, &sipType_QCameraImageProcessing, -1, 12},
        {sipName_QMediaPlaylist, &sipType_QMediaPlaylist, -1, 13},
        {sipName_QMediaService, &sipType_QMediaService, -1, 14},
        {sipName_QRadioData, &sipType_QRadioData, -1, 15},
        {sipName_QSound, &sipType_QSound, -1, 16},
        {sipName_QSoundEffect, &sipType_QSoundEffect, -1, 17},
        {sipName_QVideoProbe, &sipType_QVideoProbe, -1, -1},
        {sipName_QAudioDecoder, &sipType_QAudioDecoder, -1, 19},
        {sipName_QCamera, &sipType_QCamera, -1, 20},
        {sipName_QMediaPlayer, &sipType_QMediaPlayer, -1, 21},
        {sipName_QRadioTuner, &sipType_QRadioTuner, -1, -1},
        {sipName_QAudioDecoderControl, &sipType_QAudioDecoderControl, -1, 23},
        {sipName_QAudioEncoderSettingsControl, &sipType_QAudioEncoderSettingsControl, -1, 24},
        {sipName_QAudioInputSelectorControl, &sipType_QAudioInputSelectorControl, -1, 25},
        {sipName_QAudioOutputSelectorControl, &sipType_QAudioOutputSelectorControl, -1, 26},
    #if QT_VERSION >= 0x050600
        {sipName_QAudioRoleControl, &sipType_QAudioRoleControl, -1, 27},
    #else
        {0, 0, -1, 27},
    #endif
        {sipName_QCameraCaptureBufferFormatControl, &sipType_QCameraCaptureBufferFormatControl, -1, 28},
        {sipName_QCameraCaptureDestinationControl, &sipType_QCameraCaptureDestinationControl, -1, 29},
        {sipName_QCameraControl, &sipType_QCameraControl, -1, 30},
        {sipName_QCameraExposureControl, &sipType_QCameraExposureControl, -1, 31},
        {sipName_QCameraFeedbackControl, &sipType_QCameraFeedbackControl, -1, 32},
        {sipName_QCameraFlashControl, &sipType_QCameraFlashControl, -1, 33},
        {sipName_QCameraFocusControl, &sipType_QCameraFocusControl, -1, 34},
        {sipName_QCameraImageCaptureControl, &sipType_QCameraImageCaptureControl, -1, 35},
        {sipName_QCameraImageProcessingControl, &sipType_QCameraImageProcessingControl, -1, 36},
        {sipName_QCameraInfoControl, &sipType_QCameraInfoControl, -1, 37},
        {sipName_QCameraLocksControl, &sipType_QCameraLocksControl, -1, 38},
        {sipName_QCameraViewfinderSettingsControl, &sipType_QCameraViewfinderSettingsControl, -1, 39},
        {sipName_QCameraViewfinderSettingsControl2, &sipType_QCameraViewfinderSettingsControl2, -1, 40},
        {sipName_QCameraZoomControl, &sipType_QCameraZoomControl, -1, 41},
    #if QT_VERSION >= 0x050b00
        {sipName_QCustomAudioRoleControl, &sipType_QCustomAudioRoleControl, -1, 42},
    #else
        {0, 0, -1, 42},
    #endif
        {sipName_QImageEncoderControl, &sipType_QImageEncoderControl, -1, 43},
        {sipName_QMediaAudioProbeControl, &sipType_QMediaAudioProbeControl, -1, 44},
        {sipName_QMediaAvailabilityControl, &sipType_QMediaAvailabilityControl, -1, 45},
        {sipName_QMediaContainerControl, &sipType_QMediaContainerControl, -1, 46},
        {sipName_QMediaGaplessPlaybackControl, &sipType_QMediaGaplessPlaybackControl, -1, 47},
        {sipName_QMediaNetworkAccessControl, &sipType_QMediaNetworkAccessControl, -1, 48},
        {sipName_QMediaPlayerControl, &sipType_QMediaPlayerControl, -1, 49},
        {sipName_QMediaRecorderControl, &sipType_QMediaRecorderControl, -1, 50},
        {sipName_QMediaStreamsControl, &sipType_QMediaStreamsControl, -1, 51},
        {sipName_QMediaVideoProbeControl, &sipType_QMediaVideoProbeControl, -1, 52},
        {sipName_QMetaDataReaderControl, &sipType_QMetaDataReaderControl, -1, 53},
        {sipName_QMetaDataWriterControl, &sipType_QMetaDataWriterControl, -1, 54},
        {sipName_QRadioDataControl, &sipType_QRadioDataControl, -1, 55},
        {sipName_QRadioTunerControl, &sipType_QRadioTunerControl, -1, 56},
        {sipName_QVideoDeviceSelectorControl, &sipType_QVideoDeviceSelectorControl, -1, 57},
        {sipName_QVideoEncoderSettingsControl, &sipType_QVideoEncoderSettingsControl, -1, 58},
        {sipName_QVideoRendererControl, &sipType_QVideoRendererControl, -1, 59},
        {sipName_QVideoWindowControl, &sipType_QVideoWindowControl, -1, -1},
        {sipName_QAudioRecorder, &sipType_QAudioRecorder, -1, -1},
    };
    
    int i = 0;
    
    sipType = NULL;
    
    do
    {
        struct class_graph *cg = &graph[i];
    
        if (cg->name != NULL && sipCpp->inherits(cg->name))
        {
            sipType = *cg->type;
            i = cg->yes;
        }
        else
            i = cg->no;
    }
    while (i >= 0);
%End

public:
    enum Error
    {
        NoError,
        UnsupportedFormatError,
        IncorrectFormatError,
        StoppedError,
        ResourceError,
    };

    explicit QAbstractVideoSurface(QObject *parent /TransferThis/ = 0);
    virtual ~QAbstractVideoSurface();
    virtual QList<QVideoFrame::PixelFormat> supportedPixelFormats(QAbstractVideoBuffer::HandleType type = QAbstractVideoBuffer::NoHandle) const = 0;
    virtual bool isFormatSupported(const QVideoSurfaceFormat &format) const;
    virtual QVideoSurfaceFormat nearestFormat(const QVideoSurfaceFormat &format) const;
    QVideoSurfaceFormat surfaceFormat() const;
    virtual bool start(const QVideoSurfaceFormat &format);
    virtual void stop();
    bool isActive() const;
    virtual bool present(const QVideoFrame &frame) = 0;
    QAbstractVideoSurface::Error error() const;

signals:
    void activeChanged(bool active);
    void surfaceFormatChanged(const QVideoSurfaceFormat &format);
    void supportedFormatsChanged();

protected:
    void setError(QAbstractVideoSurface::Error error);

public:
    QSize nativeResolution() const;

protected:
    void setNativeResolution(const QSize &resolution);

signals:
    void nativeResolutionChanged(const QSize &);
};
