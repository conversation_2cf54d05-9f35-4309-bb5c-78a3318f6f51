# 📁 Structure du Projet GSCOM

## 🏗️ Vue d'Ensemble

```
GSCOM/
├── 📄 Documentation
├── 🐍 Code Source Python
├── 🗄️ Base de Données
├── 🎨 Ressources
├── 🧪 Tests
├── 🛠️ Scripts Utilitaires
└── 🔧 Configuration
```

## 📂 Structure Détaillée

```
GSCOM/
│
├── 📋 FICHIERS DE DOCUMENTATION
│   ├── README.md                    # Guide d'installation et architecture
│   ├── GUIDE_UTILISATEUR.md         # Manuel d'utilisation complet
│   ├── PRESENTATION.md              # Présentation du projet
│   ├── CHANGELOG.md                 # Historique des versions
│   ├── LICENSE.md                   # Licence et crédits
│   └── STRUCTURE.md                 # Ce fichier - structure du projet
│
├── 🚀 POINT D'ENTRÉE
│   └── main.py                      # Application principale
│
├── 🛠️ SCRIPTS UTILITAIRES
│   ├── simple_init.py               # Initialisation rapide
│   ├── init_database.py             # Initialisation complète
│   ├── deploy.py                    # Script de déploiement
│   └── test_gscom.py                # Suite de tests unitaires
│
├── 📦 CONFIGURATION
│   └── requirements.txt             # Dépendances Python
│
├── 🎨 RESSOURCES
│   └── resources/
│       ├── icons/                   # Icônes de l'application
│       │   └── README.md            # Guide des icônes
│       ├── translations/            # Fichiers de traduction
│       └── templates/               # Modèles de documents
│
├── 🐍 CODE SOURCE
│   └── src/
│       ├── __init__.py              # Package principal
│       │
│       ├── 🔧 CORE (Configuration et Utilitaires)
│       │   ├── __init__.py
│       │   ├── config.py            # Configuration centralisée
│       │   └── logger.py            # Système de journalisation
│       │
│       ├── 🗄️ DAL (Data Access Layer)
│       │   ├── __init__.py
│       │   ├── database.py          # Gestionnaire de base de données
│       │   └── models/              # Modèles de données SQLAlchemy
│       │       ├── __init__.py      # Import de tous les modèles
│       │       ├── user.py          # Utilisateurs, rôles, permissions
│       │       ├── client.py        # Clients et fournisseurs
│       │       ├── product.py       # Produits, catégories, unités
│       │       ├── commercial.py    # Devis, commandes, factures
│       │       ├── stock.py         # Mouvements de stock, inventaires
│       │       ├── accounting.py    # Comptabilité, plan comptable
│       │       └── company.py       # Paramètres entreprise
│       │
│       ├── 💼 BLL (Business Logic Layer)
│       │   ├── __init__.py
│       │   ├── base_service.py      # Service de base (CRUD générique)
│       │   ├── user_service.py      # Service utilisateurs et sécurité
│       │   ├── client_service.py    # Service clients/fournisseurs
│       │   ├── product_service.py   # Service produits
│       │   ├── commercial_service.py # Service commercial
│       │   ├── stock_service.py     # Service stock
│       │   ├── inventory_service.py # Service inventaire
│       │   └── accounting_service.py # Service comptabilité
│       │
│       └── 🎨 UI (User Interface)
│           ├── __init__.py
│           ├── login_window.py      # Fenêtre de connexion
│           ├── main_window.py       # Fenêtre principale
│           ├── components/          # Composants réutilisables
│           │   ├── __init__.py
│           │   ├── dialogs.py       # Boîtes de dialogue
│           │   ├── widgets.py       # Widgets personnalisés
│           │   └── tables.py        # Tableaux de données
│           └── modules/             # Interfaces des modules
│               ├── __init__.py
│               ├── dashboard.py     # Tableau de bord
│               ├── commercial.py    # Interface commerciale
│               ├── clients.py       # Interface clients
│               ├── products.py      # Interface produits
│               ├── stock.py         # Interface stock
│               ├── inventory.py     # Interface inventaire
│               ├── accounting.py    # Interface comptabilité
│               └── settings.py      # Interface paramètres
│
├── 🗄️ DONNÉES (Générées automatiquement)
│   ├── ~/.gscom/                    # Répertoire utilisateur
│   │   ├── config.json              # Configuration utilisateur
│   │   ├── gscom.db                 # Base de données SQLite
│   │   └── logs/                    # Fichiers de logs
│   │       └── gscom_YYYYMMDD.log   # Logs quotidiens
│   │
│   ├── backups/                     # Sauvegardes automatiques
│   └── exports/                     # Fichiers exportés
│
└── 🔧 ENVIRONNEMENT VIRTUEL
    └── venv/                        # Environnement Python isolé
        ├── Scripts/                 # Exécutables (Windows)
        ├── Lib/                     # Bibliothèques Python
        └── Include/                 # Headers C/C++
```

## 📊 Statistiques du Code

### 📈 Répartition par Couches

| Couche | Fichiers | Lignes | Responsabilité |
|--------|----------|--------|----------------|
| **UI** | 8+ | ~1,200 | Interface utilisateur |
| **BLL** | 8+ | ~800 | Logique métier |
| **DAL** | 8+ | ~1,000 | Accès aux données |
| **Core** | 2 | ~200 | Configuration et logs |
| **Total** | **26+** | **~3,200** | **Application complète** |

### 🗄️ Modèles de Données

| Modèle | Tables | Relations | Fonctionnalité |
|--------|--------|-----------|----------------|
| **User** | 3 | Many-to-Many | Authentification |
| **Client** | 2 | One-to-Many | CRM |
| **Product** | 3 | Hiérarchique | Catalogue |
| **Commercial** | 8 | Complexes | Ventes |
| **Stock** | 3 | One-to-Many | Inventaire |
| **Accounting** | 3 | One-to-Many | Comptabilité |
| **Company** | 2 | Simple | Configuration |
| **Total** | **24** | **Variées** | **Gestion complète** |

## 🔄 Flux de Données

### 📊 Architecture en Couches

```
┌─────────────────────────────────────┐
│           UI (Présentation)         │ ← PyQt5, CSS, Animations
│  ┌─────────────────────────────────┐ │
│  │ login_window.py                 │ │
│  │ main_window.py                  │ │
│  │ modules/*.py                    │ │
│  └─────────────────────────────────┘ │
└─────────────────┬───────────────────┘
                  │ Appels de méthodes
┌─────────────────▼───────────────────┐
│        BLL (Logique Métier)         │ ← Services, Validation
│  ┌─────────────────────────────────┐ │
│  │ *_service.py                    │ │
│  │ Règles de gestion               │ │
│  │ Validation des données          │ │
│  └─────────────────────────────────┘ │
└─────────────────┬───────────────────┘
                  │ Requêtes ORM
┌─────────────────▼───────────────────┐
│      DAL (Accès aux Données)        │ ← SQLAlchemy, Modèles
│  ┌─────────────────────────────────┐ │
│  │ models/*.py                     │ │
│  │ database.py                     │ │
│  │ Relations et contraintes        │ │
│  └─────────────────────────────────┘ │
└─────────────────┬───────────────────┘
                  │ SQL
┌─────────────────▼───────────────────┐
│         Base de Données             │ ← SQLite/PostgreSQL
│  ┌─────────────────────────────────┐ │
│  │ Tables relationnelles           │ │
│  │ Index et contraintes            │ │
│  │ Données persistantes            │ │
│  └─────────────────────────────────┘ │
└─────────────────────────────────────┘
```

### 🔄 Services Transverses

```
┌─────────────────────────────────────┐
│         Services Transverses        │
├─────────────────────────────────────┤
│ 🔐 Sécurité (bcrypt, sessions)      │ ← Toutes les couches
│ 📝 Logs (rotation, niveaux)         │ ← Toutes les couches
│ ⚙️ Configuration (JSON, cache)      │ ← Toutes les couches
│ 💾 Sauvegarde (auto, manuelle)      │ ← DAL principalement
│ 🌍 i18n (traductions)               │ ← UI principalement
└─────────────────────────────────────┘
```

## 🚀 Points d'Entrée

### 🎯 Scripts Principaux

| Script | Usage | Description |
|--------|-------|-------------|
| `main.py` | Production | Lance l'application complète |
| `simple_init.py` | Installation | Initialisation rapide pour tests |
| `init_database.py` | Installation | Initialisation complète avec permissions |
| `deploy.py` | Déploiement | Installation automatisée |
| `test_gscom.py` | Développement | Suite de tests unitaires |

### 🔧 Configuration

| Fichier | Type | Contenu |
|---------|------|---------|
| `requirements.txt` | Dépendances | Packages Python requis |
| `~/.gscom/config.json` | Configuration | Paramètres utilisateur |
| `src/core/config.py` | Code | Gestionnaire de configuration |

## 📦 Dépendances

### 🔧 Essentielles

```python
PyQt5>=5.15.0          # Interface graphique
SQLAlchemy>=2.0.0      # ORM base de données
bcrypt>=4.1.0          # Hashage sécurisé
```

### 📊 Optionnelles

```python
reportlab>=4.0.0       # Génération PDF
matplotlib>=3.8.0      # Graphiques
pandas>=2.1.0          # Analyse de données
openpyxl>=3.1.0        # Export Excel
python-barcode>=0.15.0 # Codes-barres
```

## 🎯 Conventions de Nommage

### 📁 Fichiers et Dossiers

- **snake_case** pour les fichiers Python (`user_service.py`)
- **lowercase** pour les dossiers (`models`, `ui`)
- **UPPERCASE** pour les constantes et documentation (`README.md`)

### 🐍 Code Python

- **PascalCase** pour les classes (`UserService`, `LoginWindow`)
- **snake_case** pour les fonctions et variables (`get_user`, `current_user`)
- **UPPER_SNAKE_CASE** pour les constantes (`DEFAULT_THEME`)

### 🗄️ Base de Données

- **snake_case** pour les tables (`users`, `invoice_lines`)
- **snake_case** pour les colonnes (`first_name`, `created_at`)
- **id** pour les clés primaires
- **_id** pour les clés étrangères (`user_id`)

## 🔍 Navigation dans le Code

### 🎯 Pour Comprendre l'Architecture

1. **Commencer par** : `README.md` (vue d'ensemble)
2. **Point d'entrée** : `main.py` (démarrage)
3. **Configuration** : `src/core/config.py`
4. **Modèles** : `src/dal/models/__init__.py`
5. **Services** : `src/bll/base_service.py`
6. **Interface** : `src/ui/login_window.py`

### 🔧 Pour Ajouter une Fonctionnalité

1. **Modèle** : Ajouter dans `src/dal/models/`
2. **Service** : Créer dans `src/bll/`
3. **Interface** : Ajouter dans `src/ui/modules/`
4. **Navigation** : Mettre à jour `main_window.py`
5. **Tests** : Ajouter dans `test_gscom.py`

### 🐛 Pour Déboguer

1. **Logs** : Consulter `~/.gscom/logs/`
2. **Configuration** : Vérifier `~/.gscom/config.json`
3. **Base de données** : Examiner `~/.gscom/gscom.db`
4. **Tests** : Lancer `python test_gscom.py`

---

**GSCOM** - *Structure modulaire et extensible pour une maintenance facile* 🏗️

*Cette structure respecte les bonnes pratiques de développement Python et facilite l'évolution future du projet.*
